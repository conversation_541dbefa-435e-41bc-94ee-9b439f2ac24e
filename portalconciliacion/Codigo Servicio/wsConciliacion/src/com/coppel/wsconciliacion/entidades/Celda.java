/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.entidades;

import java.io.IOException;

import com.itextpdf.io.font.FontConstants;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.layout.border.Border;
import com.itextpdf.layout.border.SolidBorder;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.VerticalAlignment;

/**
 * Clase que se usa como modelo de datos para las celdas pdf de la cifra de control.
 * <AUTHOR>
 *
 */
public class Celda {
	private String contenido;
	private Table tabla;
	private PdfFont fuente;
	private boolean esNegrita;
	private TextAlignment alineamientoHorizontal;
	private VerticalAlignment alineamientoVertical;
	private float tamano;
	private Border border;
	
	/**
	 * Constructor simple y vacio de la clase Celda.
	 */
	public Celda(){
		try {
			contenido = "";
			tabla = new Table(0);
			fuente = PdfFontFactory.createFont(FontConstants.HELVETICA);
			esNegrita = false;
			alineamientoHorizontal = TextAlignment.CENTER;
			alineamientoVertical = VerticalAlignment.MIDDLE;
			tamano = 8.0f;
			border = new SolidBorder(1);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.getMessage();
		}
	}
	
	/**
	 * Constructor de la clase Celda que inicia las propiedades con los valores recibidos.
	 * @param contenido es el valor que se desplegar� en la celda.
	 * @param tabla la tabla a la cual se anexara la celda.
	 * @param fuente es el tipo de letra que contendra la celda.
	 * @param esNegrita determina si es negrita o no.
	 * @param alineamientoHorizontal posici�n horizontal del texto.
	 * @param alineamientoVertical posici�n vertical del texto.
	 * @param tamano tamano de la letra dentro de la celda.
	 */
	public Celda(String contenido, Table tabla, PdfFont fuente, boolean esNegrita,
			TextAlignment alineamientoHorizontal, VerticalAlignment alineamientoVertical, float tamano){
		 this.contenido = contenido;
		 this.tabla = tabla;
		 this.fuente = fuente;
		 this.esNegrita = esNegrita;
		 this.alineamientoHorizontal = alineamientoHorizontal;
		 this.alineamientoVertical = alineamientoVertical;
		 this.tamano = tamano;
	}
	public String getContenido() {
		return contenido;
	}
	public void setContenido(String contenido) {
		this.contenido = contenido;
	}

	public Table getTabla() {
		return tabla;
	}

	public void setTabla(Table tabla) {
		this.tabla = tabla;
	}

	public PdfFont getFuente() {
		return fuente;
	}

	public void setFuente(PdfFont fuente) {
		this.fuente = fuente;
	}

	public boolean isEsNegrita() {
		return esNegrita;
	}

	public void setEsNegrita(boolean esNegrita) {
		this.esNegrita = esNegrita;
	}

	public TextAlignment getAlineamientoHorizontal() {
		return alineamientoHorizontal;
	}

	public void setAlineamientoHorizontal(TextAlignment alineamientoHorizontal) {
		this.alineamientoHorizontal = alineamientoHorizontal;
	}

	public VerticalAlignment getAlineamientoVertical() {
		return alineamientoVertical;
	}

	public void setAlineamientoVertical(VerticalAlignment alineamientoVertical) {
		this.alineamientoVertical = alineamientoVertical;
	}

	public float getTamano() {
		return tamano;
	}

	public void setTamano(float tamano) {
		this.tamano = tamano;
	}

	public Border getBorder() {
		return border;
	}

	public void setBorder(Border border) {
		this.border = border;
	}
}
