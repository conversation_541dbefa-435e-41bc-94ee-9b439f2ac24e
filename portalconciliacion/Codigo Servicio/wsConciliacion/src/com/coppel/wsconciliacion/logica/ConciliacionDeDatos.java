/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.logica;

import com.coppel.wsconciliacion.accesodatos.AccesoDatos;
import com.coppel.wsconciliacion.conexion.ConexionConciliacion;
import com.coppel.wsconciliacion.constantes.Constantes;
import com.coppel.wsconciliacion.entidades.BitacoraInterfase;
import com.coppel.wsconciliacion.entidades.CabeceraCifra;
import com.coppel.wsconciliacion.entidades.Celda;
import com.coppel.wsconciliacion.entidades.CifraControl;
import com.coppel.wsconciliacion.entidades.Configuracion;
import com.coppel.wsconciliacion.entidades.DatoConciliatorio;
import com.coppel.wsconciliacion.entidades.EmpresaHomologada;
import com.coppel.wsconciliacion.entidades.LogTiendas;
import com.coppel.wsconciliacion.enumerador.EnumConfiguracion;
import com.coppel.wsconciliacion.utilidades.Utilidades;
import com.itextpdf.io.IOException;
import com.itextpdf.io.font.constants.StandardFonts;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.UnitValue;
import com.itextpdf.layout.property.VerticalAlignment;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;

import jcifs.smb.NtlmPasswordAuthentication;
import jcifs.smb.SmbFile;
import jcifs.smb.SmbFileOutputStream;
import jcifs.util.Base64;

import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;

import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
//import java.nio.file.Files;
//import java.nio.file.Path;
//import java.nio.file.Paths;
import java.sql.ResultSet;
import java.sql.SQLException;

import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;

/**
 * Clase que se encarga de las reglas de negocio de la conciliacion y de manejar
 * los valores obtenidos de la clase de AccesoDatos, tambien se encarga de la
 * escritura y el envio de los archivos de conciliacion.
 *
 * <AUTHOR>
 *
 */
public class ConciliacionDeDatos {
	String archivoDestino = "cifrasdecontrol.pdf";
	AccesoDatos accesoDatos;
	List<Configuracion> configuracion;
	int tiendasProcesadas = 0;
	String correoDestinatario;
	String correoOrigen;
	String servidorCorreo;
	String usuarioCorreo;
	String contrasenaCorreo;
	String mensajeCorreo;
	String sujetoCorreo;
	String mensajeCorreoNegativo;
	String sujetoCorreoNegativo;
	String nombreArchivo;
	String usuarioConnect;
	String contrasenaConnect;
	String destinoConnect;
	String dominioConnect;
	String destinoTemporal;
	String carpetaOrigen;
	String carpetaPagos;
	String carpetaCifras;
	String puertoCorreo;
	String rutaSFTP;
	String usuarioSFTP;
	String contrasenaSFTP;
	String portSFTP;
	String hostSFTP;
	String privateKeySFTP;
	String usuarioApiCorreos;
	String contrasenaApiCorreos;
	String pathApiCorreos;
	String pathApiTokenCorreos;
	String conectDirectSFTP;
	String errorSftp;
	String errorConect;
	int totalOperaciones = 0;
	int totalTiendas = 0;
	int montoTotal = 0;
	int montoTotalRemanente = 0;
	int totalTiendasRemanentes = 0;
	Date fechaConciliada;
	int proceso = 0;
	CifraControl cifraControl;
	public BitacoraInterfase interfaz;
	public int ProcesoWebService = 0;
	/**
	 * Constructor de la clase ConciliacionDeDatos, instancia la clase de
	 * AccesoDatos y inicializa las variables de la configuracion.
	 *
	 * @throws Exception
	 * @throws java.io.IOException
	 * @throws SecurityException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 */
	public ConciliacionDeDatos(int proceso) throws Exception {
		try {
			this.proceso = proceso;
			@SuppressWarnings("unused")
			ConexionConciliacion cc = new ConexionConciliacion();
			ConexionConciliacion.escribirLog(proceso, "--Iniciar aplicacion --");
		} catch (SecurityException | java.io.IOException | NullPointerException e) {
			escribirArchivoEstado("2|Ocurrio un error al crear el log.");
			throw new Exception("2|" + e.getMessage());
		}

	}

	public boolean cargarConfiguracion() throws SecurityException, NullPointerException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso, "--Iniciar aplicacion --");
		ConexionConciliacion.escribirLog(proceso, "--Aplicacion WsConciliacion Version 2.0.1 --");
		boolean exito = false;
		Configuracion configuracionTemporal;
		try {
			accesoDatos = new AccesoDatos(proceso);
			obtenerConfiguracion();
			configuracionTemporal = new Configuracion();
			if (configuracion.size() != 0) {

				//ConexionConciliacion.escribirLog(proceso,"2| entro a cargar configuracion => configuracion.size");

				DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
				Date time = new Date();

				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.CorreoDestino.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				correoDestinatario = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo correoDestinatario => " + correoDestinatario);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.CorreoOrigen.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				correoOrigen = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo correoOrigen => " + correoOrigen);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.ServidorCorreo.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				servidorCorreo = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo servidorCorreo => " + servidorCorreo);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.UsuarioCorreo.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				usuarioCorreo = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo usuarioCorreo => " + usuarioCorreo);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.ContrasenaCorreo.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				contrasenaCorreo = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo contrasenaCorreo => " + contrasenaCorreo);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.MensajeCorreo.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				mensajeCorreo = configuracionTemporal.getValor1() + configuracionTemporal.getValor2();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo mensajeCorreo => " + mensajeCorreo);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.SujetoCorreo.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				sujetoCorreo = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo sujetoCorreo => " + sujetoCorreo);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.RutaConnect.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				destinoConnect = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo destinoConnect => " + destinoConnect);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.UsuarioConnect.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				usuarioConnect = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo usuarioConnect => " + usuarioConnect);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.ContrasenaConnect.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				contrasenaConnect = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo contrasenaConnect => " + contrasenaConnect);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.UsuarioApiCorreos.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				usuarioApiCorreos = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo usuarioApiCorreos => " + usuarioApiCorreos);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.ContrasenaApiCorreos.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				contrasenaApiCorreos = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo contrasenaApiCorreos => " + contrasenaApiCorreos);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.PathApiTokenCorreos.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				pathApiTokenCorreos = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo pathApiTokenCorreos => " + pathApiTokenCorreos);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.PathApiCorreos.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				pathApiCorreos = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo pathApiCorreos => " + pathApiCorreos);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.DominioDirect.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				dominioConnect = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo dominioConnect => " + dominioConnect);

				//carpetaPagos = configuracionTemporal.getValor1();

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.CarpetaCifras.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				carpetaCifras = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo carpetaCifras => " + carpetaCifras);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.PuertoCorreo.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				puertoCorreo = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo puertoCorreo => " + puertoCorreo);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.MensajeCorreoNegativo.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				mensajeCorreoNegativo = configuracionTemporal.getValor1() + configuracionTemporal.getValor2();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo mensajeCorreoNegativo => " + mensajeCorreoNegativo);

				configuracionTemporal = new Configuracion();
				configuracionTemporal.setIduConfiguracion(EnumConfiguracion.AsuntoCorreoNegativo.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				sujetoCorreoNegativo = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo sujetoCorreoNegativo => " + sujetoCorreoNegativo);
//
				configuracionTemporal = new Configuracion();
                configuracionTemporal.setIduConfiguracion(EnumConfiguracion.RutaSftp.value);
                configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
                rutaSFTP = configuracionTemporal.getValor1();

                //ConexionConciliacion.escribirLog(proceso,"se obtuvo rutaSFTP => " + rutaSFTP);

                configuracionTemporal = new Configuracion();
                configuracionTemporal.setIduConfiguracion(EnumConfiguracion.UsuarioSftp.value);
                configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
                usuarioSFTP = configuracionTemporal.getValor1();

                //ConexionConciliacion.escribirLog(proceso,"se obtuvo usuarioSFTP => " + usuarioSFTP);

                configuracionTemporal = new Configuracion();
                configuracionTemporal.setIduConfiguracion(EnumConfiguracion.ContrasenaSftp.value);
                configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
                contrasenaSFTP = configuracionTemporal.getValor1();

                //ConexionConciliacion.escribirLog(proceso,"se obtuvo contrasenaSFTP => " + contrasenaSFTP);

                configuracionTemporal = new Configuracion();
                configuracionTemporal.setIduConfiguracion(EnumConfiguracion.PortSftp.value);
                configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
                portSFTP = configuracionTemporal.getValor1();

                //ConexionConciliacion.escribirLog(proceso,"se obtuvo portSFTP => " + portSFTP);

                configuracionTemporal = new Configuracion();
                configuracionTemporal.setIduConfiguracion(EnumConfiguracion.HostSftp.value);
                configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
                hostSFTP = configuracionTemporal.getValor1();

                //ConexionConciliacion.escribirLog(proceso,"se obtuvo portSFTP => " + portSFTP);

                configuracionTemporal = new Configuracion();
                configuracionTemporal.setIduConfiguracion(EnumConfiguracion.PrivateKeySftp.value);
                configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
                privateKeySFTP = configuracionTemporal.getValor1();

                //ConexionConciliacion.escribirLog(proceso,"se obtuvo privateKeySFTP => " + privateKeySFTP);

                configuracionTemporal = new Configuracion();
                configuracionTemporal.setIduConfiguracion(EnumConfiguracion.ConectDirectSFTP.value);
				configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
				conectDirectSFTP = configuracionTemporal.getValor1();

				//ConexionConciliacion.escribirLog(proceso,"se obtuvo conectDirectSFTP => " + conectDirectSFTP);

				if(nombreArchivo == null)
				{
					nombreArchivo = "conciliaciondetalle" + dateFormat.format(time) + ".txt";
				}
				if(carpetaPagos == null)
				{
					//ConexionConciliacion.escribirLog(proceso,"entro a carpetapagos == null ");
					configuracionTemporal = new Configuracion();
					configuracionTemporal.setIduConfiguracion(EnumConfiguracion.CarpetaPagos.value);
					configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
					carpetaPagos = configuracionTemporal.getValor1() + nombreArchivo;
				}
				carpetaOrigen = nombreArchivo;
				archivoDestino = "conciliacioncifra" + dateFormat.format(time) + ".pdf";
				carpetaCifras = carpetaCifras + archivoDestino;

				String nombreOtroArchivo = nombreArchivo;
				nombreOtroArchivo = nombreOtroArchivo.replace("conciliaciondetalle", "conciliacioncifra");
				String textoArchivos = nombreArchivo + " y " + nombreOtroArchivo;
				mensajeCorreo = mensajeCorreo.replace("\\n", "\n");
				mensajeCorreo = mensajeCorreo.replace("{0}", textoArchivos);
				mensajeCorreo = mensajeCorreo.replace("{1}", destinoConnect.replace("smb:/", "").replace("\\", "/"));
				mensajeCorreo = mensajeCorreo.replace("{2}", archivoDestino);

				mensajeCorreoNegativo = mensajeCorreoNegativo.replace("{0}", textoArchivos);

				exito = true;
				//ConexionConciliacion.escribirLog(proceso,"exito =>  " + exito);
			}
		} catch (Exception e) {
			escribirArchivoEstado("2|Ocurrio un error al obtener la configuracion de la base de datos ingresos.");
			e.getMessage();
			ConexionConciliacion.escribirLog(proceso, "--" + e.getMessage() + " --");
		}
		return exito;
	}

	/**
	 * Metodo para generar la cifra de control en pdf.
	 *
	 * @param ruta
	 *            recibe un string que es la direccion donde se escribira el
	 *            archivo.
	 * @return un string con el nombre del archivo.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 * @throws java.io.IOException
	 */
	public Boolean GenerarCifrasControl(String ruta)
			throws SecurityException, NullPointerException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo generarCifrasControl --");
		Boolean estado = false;
		try {
			LogTiendas logTiendasPendiente = null;
			LogTiendas fechaPendiente = null;
			escribirArchivoEstado("5|Generando cifra de control.");
	        PdfWriter pdfEscritor = new PdfWriter(ruta);
	        PdfDocument pdfDocumento = new PdfDocument(pdfEscritor);
	        Document documento = new Document(pdfDocumento,PageSize.LETTER);
	        PdfFont tituloFuente = PdfFontFactory.createFont(StandardFonts.HELVETICA_BOLD);
	        PdfFont estandarFuente = PdfFontFactory.createFont(StandardFonts.HELVETICA);
	        Text title = new Text("Cifra de Control de Pago de Servicios").setFont(tituloFuente)
	        		.setFontColor(ColorConstants.BLACK).setFontSize(14.0f);
	        Paragraph p = new Paragraph().add(title);
	        p.setTextAlignment(TextAlignment.CENTER);
	        int montoTotalRemanenteCifra = 0;
	        DecimalFormat moneda = new DecimalFormat("#,###.00");
	        DecimalFormat decimal = new DecimalFormat("#");
	        decimal.setRoundingMode(RoundingMode.HALF_DOWN);
	        int tamanoCabecera = cifraControl.getCabecera().size();
			p.add("\n\n");
			documento.add(p);
			Celda modeloCelda;
			LogTiendas fechaRemanente;
			CabeceraCifra cabeceraCifra;
			Table tablaReportePrimeraMitad = new Table(8);
			tablaReportePrimeraMitad.setWidth(UnitValue.createPercentValue(100));
			String[] cabeceraReportePrimeraMitad = { "Dia", "Total de Tiendas a procesar", "Tiendas Procesadas",
					"Porcentaje", "Monto generado", "Tiendas Remanentes", "Monto generado", "Fecha Remanente" };
			for (int i = 0; i < cabeceraReportePrimeraMitad.length; i++) {
				modeloCelda = new Celda(cabeceraReportePrimeraMitad[i], tablaReportePrimeraMitad, tituloFuente, true,
						TextAlignment.CENTER, VerticalAlignment.BOTTOM, 9.0f);
				celdaNormal(modeloCelda, true);
			}
			if (tamanoCabecera < cifraControl.getFechaRemanente().size()) {
				tamanoCabecera = cifraControl.getFechaRemanente().size();
			}

			for (int i = 0; i < tamanoCabecera; i++) {

				if (i >= cifraControl.getCabecera().size()) {
					for (int j = 0; j <= 3; j++) {
						modeloCelda = new Celda("", tablaReportePrimeraMitad, estandarFuente, true,
								TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, true);
					}
				} else {
					cabeceraCifra = cifraControl.getCabecera().get(i);
					modeloCelda = new Celda(cabeceraCifra.getDiaConciliado().toString(), tablaReportePrimeraMitad,
							estandarFuente, true, TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
					celdaNormal(modeloCelda, true);


					cabeceraCifra.setNumTotalTiendas(cabeceraCifra.getTiendasProcesadas() + cifraControl.getTiendaPendiente().size());
					// HERE
					// MUESTRA LA CANTIDAD DE TIENDAS TOTALES DEL CAMPO Total de
					// Tiendas
					modeloCelda = new Celda(Integer.toString(cabeceraCifra.getNumTotalTiendas()), tablaReportePrimeraMitad, //
							estandarFuente, true, //
							TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);//
					celdaNormal(modeloCelda, true);//

					modeloCelda = new Celda(cabeceraCifra.getTiendasProcesadas() + "", tablaReportePrimeraMitad, estandarFuente,
							true, TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
					celdaNormal(modeloCelda, true);

					modeloCelda = new Celda(decimal.format(cabeceraCifra.obtenerPorcentaje()), tablaReportePrimeraMitad,
							estandarFuente, true, TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
					celdaNormal(modeloCelda, true);

					modeloCelda = new Celda("$ " + moneda.format(cabeceraCifra.getMontoDelDia()), tablaReportePrimeraMitad,
							estandarFuente, true, TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
					celdaNormal(modeloCelda, true);
				}
				if (i >= cifraControl.getFechaRemanente().size()) {
					for (int j = 0; j < 3; j++) {
						modeloCelda = new Celda("", tablaReportePrimeraMitad, estandarFuente, true,
								TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, true);
					}
				} else {
					fechaRemanente = cifraControl.getFechaRemanente().get(i);
					modeloCelda = new Celda(fechaRemanente.getTotalProcesado() + "", tablaReportePrimeraMitad,
							estandarFuente, true, TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
					celdaNormal(modeloCelda, true);

					modeloCelda = new Celda("$ " + moneda.format(fechaRemanente.getMontoTotal()),
							tablaReportePrimeraMitad, estandarFuente, true, TextAlignment.CENTER,
							VerticalAlignment.BOTTOM, 8.0f);
					celdaNormal(modeloCelda, true);

					modeloCelda = new Celda(fechaRemanente.getFechaConciliacion().toString(), tablaReportePrimeraMitad,
							estandarFuente, true, TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
					celdaNormal(modeloCelda, true);
				}
			}

			documento.add(tablaReportePrimeraMitad);
			documento.add(new Paragraph().add("\n"));
			Table tablaReporteSegundaMitad = new Table(7);
			LogTiendas tiendaVaciaLogTiendas;
			tablaReporteSegundaMitad.setWidth(UnitValue.createPercentValue(100));
			int cantidadTiendasPendientes = cifraControl.getTiendaRemanente().size();
			int k = 0;
			for (int i = 0; i < cifraControl.getTiendaPendiente().size() % 3; i++) {
				tiendaVaciaLogTiendas = new LogTiendas();
				tiendaVaciaLogTiendas.setNumTienda(0);
				cifraControl.getTiendaPendiente().add(tiendaVaciaLogTiendas);
			}

			if (cantidadTiendasPendientes < cifraControl.getTiendaPendiente().size() / 3) {
				cantidadTiendasPendientes = cifraControl.getTiendaPendiente().size() / 3;
			}

			if (cifraControl.getTiendaPendiente().size() == 0) {
				for (int n = 0; n < 4; n++) {
					modeloCelda = new Celda("", tablaReporteSegundaMitad, estandarFuente, true, TextAlignment.CENTER,
							VerticalAlignment.BOTTOM, 8.0f);
					celdaNormal(modeloCelda, false);
				}
			} else {
				modeloCelda = new Celda("", tablaReporteSegundaMitad, estandarFuente, true, TextAlignment.CENTER,
						VerticalAlignment.BOTTOM, 8.0f);
				celdaNormal(modeloCelda, false);

				modeloCelda = new Celda("Tiendas pendientes de procesar", tablaReporteSegundaMitad, tituloFuente, true,
						TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
				celdaNormal(modeloCelda, false);

				modeloCelda = new Celda("", tablaReporteSegundaMitad, tituloFuente, true, TextAlignment.CENTER,
						VerticalAlignment.BOTTOM, 8.0f);
				celdaNormal(modeloCelda, false);

				modeloCelda = new Celda("", tablaReporteSegundaMitad, tituloFuente, true, TextAlignment.CENTER,
						VerticalAlignment.BOTTOM, 8.0f);
				celdaNormal(modeloCelda, false);
			}

			if (cifraControl.getTiendaRemanente().size() == 0) {
				for (int n = 0; n < 3; n++) {
					modeloCelda = new Celda("", tablaReporteSegundaMitad, estandarFuente, true, TextAlignment.CENTER,
							VerticalAlignment.BOTTOM, 8.0f);
					celdaNormal(modeloCelda, false);
				}
			} else {
				modeloCelda = new Celda("Num. de Tienda", tablaReporteSegundaMitad, tituloFuente, true,
						TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
				celdaNormal(modeloCelda, true);

				modeloCelda = new Celda("Monto generado", tablaReporteSegundaMitad, tituloFuente, true,
						TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
				celdaNormal(modeloCelda, true);

				// se QUITA TEXTO de COLUMNA DE FECHA REMANENTE
				modeloCelda = new Celda("Fecha Remanente", tablaReporteSegundaMitad, tituloFuente, true,
						TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
				// SE QUITA BORDE TEXTO de COLUMNA DE FECHA REMANENTE
				celdaNormal(modeloCelda, true);
			}

			for (int i = 0; i < cantidadTiendasPendientes + 1; i++) {
				if (i >= cifraControl.getTiendaRemanente().size()) {
					tiendaVaciaLogTiendas = null;
				} else {
					tiendaVaciaLogTiendas = cifraControl.getTiendaRemanente().get(i);
				}

				if (i * 3 >= cifraControl.getTiendaPendiente().size()) {
					for (int l = 0; l < 3; l++) {
						modeloCelda = new Celda("", tablaReporteSegundaMitad, estandarFuente, true,
								TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, false);
					}
				} else {
					// Agrega la fecha de conciliacion de forma concatenada
					logTiendasPendiente = cifraControl.getTiendaPendiente().get(k);
					fechaPendiente = cifraControl.getfechaPendiente().get(k);
					modeloCelda = new Celda(
							logTiendasPendiente.getNumTienda() + " - " + fechaPendiente.getFechaPendiente(),
							tablaReporteSegundaMitad, estandarFuente, true, TextAlignment.CENTER,
							VerticalAlignment.BOTTOM, 8.0f);
					celdaNormal(modeloCelda, true);

					if (cifraControl.getTiendaPendiente().get(k + 1).getNumTienda() == 0) {
						modeloCelda = new Celda("", tablaReporteSegundaMitad, estandarFuente, true,
								TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, false);
					} else {
						// Agrega la fecha de conciliacion de forma concatenada
						logTiendasPendiente = cifraControl.getTiendaPendiente().get(k + 1);
						fechaPendiente = cifraControl.getfechaPendiente().get(k);
						modeloCelda = new Celda(
								logTiendasPendiente.getNumTienda() + " - " + fechaPendiente.getFechaPendiente(),
								tablaReporteSegundaMitad, estandarFuente, true, TextAlignment.CENTER,
								VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, true);
					}
					if (cifraControl.getTiendaPendiente().get(k + 2).getNumTienda() == 0) {
						modeloCelda = new Celda("", tablaReporteSegundaMitad, estandarFuente, true,
								TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, false);
					} else {
						// Agrega la fecha de conciliacion de forma concatenada
						logTiendasPendiente = cifraControl.getTiendaPendiente().get(k + 2);
						fechaPendiente = cifraControl.getfechaPendiente().get(k);
						modeloCelda = new Celda(
								logTiendasPendiente.getNumTienda() + " - " + fechaPendiente.getFechaPendiente(),
								tablaReporteSegundaMitad, estandarFuente, true, TextAlignment.CENTER,
								VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, true);
					}
					k += 3;
				}

				modeloCelda = new Celda("", tablaReporteSegundaMitad, estandarFuente, true, TextAlignment.CENTER,
						VerticalAlignment.BOTTOM, 8.0f);
				celdaNormal(modeloCelda, false);

				if (i == cifraControl.getTiendaRemanente().size()) {
					if (cifraControl.getTiendaRemanente().size() > 0) {
						modeloCelda = new Celda("Suma", tablaReporteSegundaMitad, estandarFuente, true,
								TextAlignment.RIGHT, VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, false);

						modeloCelda = new Celda("$ " + moneda.format(montoTotalRemanenteCifra),
								tablaReporteSegundaMitad, tituloFuente, true, TextAlignment.RIGHT,
								VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, false);

						modeloCelda = new Celda("", tablaReporteSegundaMitad, estandarFuente, true,
								TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, false);
					} else {
						for (int n = 0; n < 3; n++) {
							modeloCelda = new Celda("", tablaReporteSegundaMitad, estandarFuente, true,
									TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
							celdaNormal(modeloCelda, false);
						}
					}

				} else {
					if (tiendaVaciaLogTiendas != null) {
						modeloCelda = new Celda(tiendaVaciaLogTiendas.getNumTienda() + "", tablaReporteSegundaMitad,
								estandarFuente, true, TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, true);

						modeloCelda = new Celda("$ " + moneda.format(tiendaVaciaLogTiendas.getMontoTotal()),
								tablaReporteSegundaMitad, estandarFuente, true, TextAlignment.RIGHT,
								VerticalAlignment.BOTTOM, 8.0f);
						celdaNormal(modeloCelda, true);
						montoTotalRemanenteCifra += tiendaVaciaLogTiendas.getMontoTotal();
						// se quita el valor de la fecha conciliacion
						modeloCelda = new Celda(tiendaVaciaLogTiendas.getFechaConciliacion().toString(),
								tablaReporteSegundaMitad, estandarFuente, true, TextAlignment.CENTER,
								VerticalAlignment.BOTTOM, 8.0f);
						// se quita borde del el valor de la fecha conciliacion
						celdaNormal(modeloCelda, true);
					} else {
						for (int n = 0; n < 3; n++) {
							modeloCelda = new Celda("", tablaReporteSegundaMitad, estandarFuente, true,
									TextAlignment.CENTER, VerticalAlignment.BOTTOM, 8.0f);
							celdaNormal(modeloCelda, false);
						}
					}

				}

			}
			documento.add(tablaReporteSegundaMitad);

			cabeceraCifra=null;
			documento.close();
			estado = true;
		} catch (java.io.IOException ex) {
			escribirArchivoEstado("2|Ocurrio un error al generar la cifra de control.");
			ex.getMessage();
			ConexionConciliacion.escribirLog(proceso, "--" + ex.getMessage() + " --");
		}

		// writer.Close();
		return estado;
	}

	/**
	 * Metodo para crear la celda en el pdf de la cifra de control.
	 *
	 * @param modeloCelda
	 *            modelo de datos del tipo Celda que tiene las caracteristicas
	 *            de la celda que creara.
	 * @param tieneBorder
	 *            valor booleano que indica si tiene borde o no la celda.
	 */
	private void celdaNormal(Celda modeloCelda, boolean tieneBorder) {
		Cell celda = new Cell();
		Border border = new SolidBorder(1);
		border.setWidth(1);
		border.setColor(ColorConstants.BLACK);
		if (tieneBorder) {
			celda.setBorder(border);
		} else {
			celda.setBorder(Border.NO_BORDER);
		}
		celda.setWidth(UnitValue.createPercentValue(100));
		celda.setHeight(20);
		Text cab = new Text(modeloCelda.getContenido()).setFont(modeloCelda.getFuente()).setFontColor(ColorConstants.BLACK)
				.setFontSize(8.0f);
		Paragraph pa = new Paragraph().add(cab);
		celda.add(pa);
		celda.setMarginRight(5);
		celda.setTextAlignment(modeloCelda.getAlineamientoHorizontal());
		celda.setVerticalAlignment(modeloCelda.getAlineamientoVertical());
		modeloCelda.getTabla().addCell(celda);
	}

	/**
	 * Metodo que para obtener la configuracion y la asigna.
	 *
	 * @return un string que indica el estado de la operacion.
	 * @throws Exception
	 * @throws java.io.IOException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 */
	public String obtenerConfiguracion() throws Exception {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo obtenerConfiguracion --");
		String estado = "";
		ResultSet resultado;
		configuracion = new ArrayList<Configuracion>();
		Configuracion conf;
		try {
			accesoDatos.conectarIngresos(false);
			resultado = accesoDatos.obtenerConfiguracionIngresos();
			while (resultado.next()) {
				conf = new Configuracion();
				conf.setIduConfiguracion(resultado.getInt("idu_configuracion"));
				conf.setDescripcion(resultado.getString("des_configuracion"));
				conf.setValor1(new String(resultado.getBytes("des_valor1"), "UTF-8"));
				conf.setValor2(resultado.getString("des_valor2"));
				configuracion.add(conf);
			}
		} catch (Exception e) {
	    	logError(e,"obtenerConfiguracion");
			ConexionConciliacion.escribirLog(proceso, "--" + e.getMessage() + " --");
		}
		try {
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();
		} catch (SQLException e) {
			e.getMessage();
			ConexionConciliacion.escribirLog(proceso, "--" + e.getMessage() + " --");
		}

		//accesoDatos.conectarCajas();
		accesoDatos.conectarIngresos(false);
		try {
			resultado = accesoDatos.obtenerTotalTiendas();
			while (resultado.next()) {
				totalTiendas = resultado.getInt("totaltiendas");
			}
		} catch (SQLException e) {
			ConexionConciliacion.escribirLog(proceso, "--" + e.getMessage() + " --");
			throw e;
		}
		try {
			accesoDatos.cerrarSentencia();
			//accesoDatos.desconectarCajas();
			accesoDatos.desconectarIngresos();
		} catch (SQLException e) {
			e.getMessage();
			ConexionConciliacion.escribirLog(proceso, "--" + e.getMessage() + " --");
		}

		return estado;
	}

	/**
	 * Metodo para llenar la coleccion de datos que poseera los movimientos a
	 * conciliar.
	 *
	 * @param datosConciliatorios
	 *            recibe una lista del modelo de datos DatoConciliatorio en la
	 *            cual se agregaran los movimientos.
	 * @param resultado
	 *            recibe un ResultSet el cual posee los datos a agregar.
	 * @return un string que contiene el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 * @throws SQLException
	 */
	public Boolean generarListaConciliatoria(List<DatoConciliatorio> datosConciliatorios, ResultSet resultado,
			int tipoMovimiento) throws SecurityException, NullPointerException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo generarListaConciliatoria --");
		DatoConciliatorio datoConciliatorio;
		Boolean estado = false;
		try {
			if (resultado != null) {
				while (resultado.next()) {
					datoConciliatorio = new DatoConciliatorio();
					datoConciliatorio.setClvDatoConci(resultado.getString("claveb"));
					datoConciliatorio.setTipoMovimiento(resultado.getString("tipomovimientob"));
					datoConciliatorio.setImporte(resultado.getString("importe"));
					datoConciliatorio.setFechaMovimiento(resultado.getDate("fecha"));
					datoConciliatorio.setNumTienda(resultado.getInt("tienda"));
					datoConciliatorio.setEfectuo(resultado.getString("efectuo").trim());
					datoConciliatorio.setClaveEmpresa("C");
					datoConciliatorio.setCiudad(resultado.getString("ciudad").trim());
					datoConciliatorio.setDescripcionPago(resultado.getString("des_tituloconsulta").trim());
					datoConciliatorio.setCaja(resultado.getString("caja"));
					datoConciliatorio.setFolio(resultado.getString("fol_sucursal").trim());
					datoConciliatorio.setRecibo(resultado.getString("recibo").trim());
					datoConciliatorio.setContrato(resultado.getString("contrato").trim());
					datoConciliatorio.setCampoFuturo1("0");
					datoConciliatorio.setCampoFuturo2("0");
					datoConciliatorio.setCampoFuturo3("0");
					datoConciliatorio.setCampoFuturo4("0");
					datosConciliatorios.add(datoConciliatorio);
				}
			}
			estado = true;
		} catch (Exception e) {
	    	logError(e,"generarListaConciliatoria");
			ConexionConciliacion.escribirLog(proceso, "--" + e.getMessage() + " --");
		}
		return estado;
	}

	/**
	 * Metodo para escribir el archivo de conciliacion.
	 *
	 * @param datosConciliatorios
	 *            recibe la lista del tipo DatoConciliatorio que contiene los
	 *            datos a escribir.
	 * @return un string que contiene el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 * @throws UnsupportedEncodingException
	 * @throws FileNotFoundException
	 */
	private Boolean escribirArchivoEstado(String estadoConciliacion)
			throws SecurityException, NullPointerException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo escribirArchivoEstado --");
		Boolean estado = false;
		PrintWriter writer = null;

		File archivoEstatus = new File(ConexionConciliacion.rutaEstatus);
		archivoEstatus.setExecutable(false);
		archivoEstatus.setReadable(true);
		archivoEstatus.setWritable(true);

		if (!archivoEstatus.exists()) {
			throw new FileNotFoundException("El archivo de estatus no existe: " + archivoEstatus);
		}else{
			try{
				writer = new PrintWriter(archivoEstatus, "UTF-8");
				writer.print(estadoConciliacion);
				writer.close();
				estado = true;
				ConexionConciliacion.escribirLog(proceso, "--termina escribirArchivoEstado :  ".concat(ConexionConciliacion.rutaEstatus));
			} catch (Exception e) {
		    	logError(e,"escribirArchivoEstado");
				ConexionConciliacion.escribirLog(proceso, "--" + e.getMessage() + " --");
			}  finally {
				if(writer != null){
					writer.close();
				}
			}
			return estado;
		}
	}

	/**
	 * Metodo para escribir el archivo de conciliacion agrupado.
	 *
	 * @param datosConciliatorios
	 *            recibe la lista del tipo DatoConciliatorio que contiene los
	 *            datos a escribir.
	 * @return un string que contiene el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 * @throws UnsupportedEncodingException
	 * @throws FileNotFoundException
	 */
	private Boolean escribirArchivoConciliacionAgrupado(List<DatoConciliatorio> datosConciliatorios)
			throws SecurityException, NullPointerException, java.io.IOException, Exception {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo escribirArchivoConciliacionAgrupado --");
		Boolean estado = false;
		DatoConciliatorio dato;
		String textoGuardar = "", fec_fechaConciliacion = "";
		int iContador = 0;
		PrintWriter writer = null;
		try {
			List<Object[]> lista = new ArrayList<>();
			// se vacia el total de movimientos en una lista tipo matriz
			for (int i = 0; i < datosConciliatorios.size(); i++) {
				dato = datosConciliatorios.get(i);
				lista.add(new Object[] { dato.getFechaMovimientoString(), dato.getNumTienda(), dato.getImporte(),
						dato.getClvDatoConci(), dato.getTipoMovimiento(), 1, dato.getClaveEmpresa() });
			}
			// se ordenan por medio una combinacion unica
			Collections.sort(lista, new Comparator<Object[]>() {
				@Override
				public int compare(Object[] o1, Object[] o2) {
					StringBuilder sb1 = new StringBuilder();
					sb1.append(o1[0]);// fecha movimiento
					sb1.append(o1[1]);// numero de tienda
					sb1.append(o1[3]);// clave
					sb1.append(o1[4]);// tipo movimiento
					sb1.append(o1[6]);// empresa

					StringBuilder sb2 = new StringBuilder();
					sb2.append(o2[0]);// fecha movimiento
					sb2.append(o2[1]);// numero de tienda
					sb2.append(o2[3]);// clave
					sb2.append(o2[4]);// tipo movimiento
					sb2.append(o2[6]);// empresa
					return sb1.toString().compareTo(sb2.toString());
				}
			});

			// Procedimiento pera hacer la agrupacion
			List<Object[]> listaAgrupada = new ArrayList<>();
			StringBuilder anterior = new StringBuilder();
			Object[] candidato = new Object[7];
			for (Object[] row : lista) {

				StringBuilder actual = new StringBuilder();
				actual.append(row[0]);// fecha movimiento
				actual.append(row[1]);// numero de tienda
				actual.append(row[3]);// clave
				actual.append(row[4]);// tipo movimiento
				actual.append(row[6]);// empresa

				if (!anterior.toString().equals(actual.toString())) {
					anterior = actual;
					candidato = new Object[] { row[0], row[1], row[2], row[3], row[4], row[5], row[6] };
					listaAgrupada.add(candidato);
				} else {
					candidato[2] = Integer.parseInt(candidato[2].toString()) + Integer.parseInt(row[2].toString());
					candidato[5] = Integer.parseInt(candidato[5].toString()) + 1;
				}
			}

			String rutapagoserviciocifra = carpetaPagos;
			rutapagoserviciocifra = rutapagoserviciocifra.replace("conciliaciondetalle", "conciliacioncifra");
			String ultimaFecha = obtenerUltimaFecha();
			if (ultimaFecha.length() >= 2) {

				fec_fechaConciliacion = ultimaFecha.substring(2);
				ConexionConciliacion.escribirLog(proceso, "-- La cadena devuelta por obtenerUltimaFecha() => "+ fec_fechaConciliacion);
			} else {
				ConexionConciliacion.escribirLog(proceso, "-- La cadena devuelta por obtenerUltimaFecha() es demasiado corta para realizar la operación de subcadena.");
			}

			writer = new PrintWriter(rutapagoserviciocifra, "UTF-8");
			for (int i = 0; i < listaAgrupada.size(); i++) {
				Object[] row = listaAgrupada.get(i);
				writer.print("" + row[0] + "|" + row[1] + "|" + row[2] + "|" + row[3].toString().trim() + "|" + row[4].toString().trim() + "|" + row[5]
						+ "|" + row[6].toString().trim() + "\r\n");


                                textoGuardar +=  "&" + row[0] + "|" + row[1] + "|" + row[2] + "|" + row[3].toString().trim() + "|"
                                                + row[4].toString().trim() + "|" + row[5] + "|" + row[6].toString().trim() + "|" + fec_fechaConciliacion + "\r\n";


                                //La siguiente estructura de control tiene la logica de insertar
                                //en segmentos de 100 en 100 para prevenir que la variable crezca en exceso
				if ((++iContador == 100) || (i == listaAgrupada.size() - 1)) {
					try {
						accesoDatos.conectarIngresos(false);
                        textoGuardar  =textoGuardar.replaceFirst("&", "");
						accesoDatos.guardarConciliacionCifraPagoDetalle(textoGuardar);
						accesoDatos.cerrarSentencia();
						accesoDatos.desconectarIngresos();
						System.gc();
						iContador = 0;
                        textoGuardar="";
					} catch (Exception ex) {
				    	logError(ex,"escribirArchivoConciliacionAgrupado");
						ConexionConciliacion.escribirLog(proceso, "--" + ex.getMessage() + " --");
						ex.getMessage();
					}
				}
				//iContador++;
			}
			estado = true;
		} catch (Exception e) {
	    	logError(e,"escribirArchivoConciliacionAgrupado");
			ConexionConciliacion.escribirLog(proceso, "--" + e.getMessage() + " --");
		} finally {
			if(writer != null)
			writer.close();
		}
		return estado;
	}

	/**
	 * Metodo para escribir el archivo de conciliacion.
	 *
	 * @param datosConciliatorios
	 *            recibe la lista del tipo DatoConciliatorio que contiene los
	 *            datos a escribir.
	 * @return un string que contiene el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 * @throws UnsupportedEncodingException
	 * @throws FileNotFoundException
	 */
	private Boolean escribirArchivoConciliacion(List<DatoConciliatorio> datosConciliatorios)
			throws SecurityException, NullPointerException, java.io.IOException, Exception {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo escribirArchivoConciliacion --");
		Boolean estado = false;
		DatoConciliatorio dato;
		DatoConciliatorio datoConciliatorioFecha;
		String textoGuardar = "", fec_fechaConciliacion = "";
		//Date dfechaConciliacion;
		DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
		int iContador = 0;
		PrintWriter writer = null;
		try {
			datoConciliatorioFecha =  datosConciliatorios.get(datosConciliatorios.size()-1);
			carpetaPagos = carpetaPagos.substring(0, carpetaPagos.length()-12);
			ConexionConciliacion.escribirLog(proceso, "--carpetaPagos => " + carpetaPagos);
			carpetaPagos = carpetaPagos + dateFormat.format(datoConciliatorioFecha.getFechaMovimiento()) + ".txt";
			ConexionConciliacion.escribirLog(proceso, "--carpetaPagos con fecha => " + carpetaPagos);
			nombreArchivo =  carpetaPagos.substring(carpetaPagos.length()-31);
			ConexionConciliacion.escribirLog(proceso, "--nombreArchivo => " + nombreArchivo);
			String ultimaFecha = obtenerUltimaFecha();
			if (ultimaFecha.length() >= 2) {
				fec_fechaConciliacion = ultimaFecha.substring(2);
			} else {
				ConexionConciliacion.escribirLog(proceso, "-- La cadena devuelta por obtenerUltimaFecha() es demasiado corta para realizar la operación de subcadena." + " --");
			}
			//fec_fechaConciliacion = obtenerUltimaFecha().substring(2);
			writer = new PrintWriter(carpetaPagos, "UTF-8");
			for (int i = 0; i < datosConciliatorios.size(); i++) {
				dato = datosConciliatorios.get(i);
				writer.print("" + dato.getClvDatoConci().trim() + "|" + dato.getTipoMovimiento().trim() + "|" + dato.getImporte() + "|"
						+ dato.getFechaMovimiento() + "|" + dato.getNumTienda() + "|" + dato.getEfectuo() + "|"
						+ dato.getClaveEmpresa() + "|" + dato.getCiudad() + "|" + dato.getDescripcionPago() + "|"
						+ dato.getCaja() + "|" + dato.getFolio() + "|" + dato.getRecibo() + "|" + dato.getContrato()
						+ "|" + dato.getCampoFuturo1() + "|" + dato.getCampoFuturo2() + "|" + dato.getCampoFuturo3()
						+ "|" + dato.getCampoFuturo4() + "|0|0| | | | |\r\n");

                                textoGuardar += "&" + dato.getClvDatoConci() + "|" + dato.getTipoMovimiento() + "|"
                                                + dato.getImporte() + "|" + dato.getFechaMovimiento() + "|" + dato.getNumTienda() + "|"
                                                + dato.getEfectuo() + "|" + dato.getClaveEmpresa() + "|" + dato.getCiudad() + "|"
                                                + dato.getDescripcionPago() + "|" + dato.getCaja() + "|" + dato.getFolio() + "|"
                                                + dato.getRecibo() + "|" + dato.getContrato() + "|" + dato.getCampoFuturo1() + "|"
                                                + dato.getCampoFuturo2() + "|" + dato.getCampoFuturo3() + "|" + dato.getCampoFuturo4()
                                                + "|0|0| | | | |" + fec_fechaConciliacion + "\r\n";


                                //La siguiente estructura de control tiene la logica de insertar
                                //en segmentos de 100 en 100 para prevenir que la variable crezca en exceso
				if ((++iContador == 100) || (i == datosConciliatorios.size() - 1)) {
					try {
						accesoDatos.conectarIngresos(false);
                        textoGuardar=textoGuardar.replaceFirst("&", "");
						accesoDatos.guardarConciliacionPagoDetalle(textoGuardar);
						accesoDatos.cerrarSentencia();
						accesoDatos.desconectarIngresos();
						System.gc();
						iContador = 0;
                        textoGuardar="";
					} catch (Exception ex) {
				    	logError(ex,"escribirArchivoConciliacion");
						ConexionConciliacion.escribirLog(proceso, "--" + ex.getMessage() + " --");
						ex.getMessage();
					}
				}
				//iContador++;

			}
			estado = true;
		} catch (Exception e) {
	    	logError(e,"escribirArchivoConciliacion");
			ConexionConciliacion.escribirLog(proceso, "--" + e.getMessage() + " --");
		}  finally {
			if(writer != null)
			writer.close();
		}
		return estado;
	}

	/**
	 * Metodo para obtener la ultima fecha en la que se concilio.
	 *
	 * @return un string que contiene la ultima fecha.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 */
	public String obtenerUltimaFecha() throws SecurityException, NullPointerException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo obtenerUltimaFecha --");
		String ultimaFecha = null;
		Configuracion configuracionTemporal;
		if (!cargarConfiguracion()) {
			ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al obtener la ultima fecha. Error al cargarConfiguracion. --");
			ultimaFecha = "2|Ocurrio un error al obtener la ultima fecha. Error al cargarConfiguracion.";
		} else {
			configuracionTemporal = new Configuracion();
			configuracionTemporal.setIduConfiguracion(EnumConfiguracion.FechaConciliacion.value);
			ConexionConciliacion.escribirLog(proceso, "-- FechaConciliacion  => " + EnumConfiguracion.FechaConciliacion.value);
			configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
			ultimaFecha = "0|" + configuracionTemporal.getValor1();
			ConexionConciliacion.escribirLog(proceso, "-- 0| ultima Fecha => " + configuracionTemporal.getValor1());
		}
		return ultimaFecha;
	}

	/**
	 * Metodo que se encargar de manejar y unir el proceso de conciliacion
	 * obtener datos, generar archivo, enviar correo, etc.
	 *
	 * @return un string indicando el estado de la operacion.
	 * @throws Exception
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws ParseException
	 * @throws java.io.IOException
	 * @throws IOException
	 */
	public String generarConciliacion() throws Exception {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo generarConciliacion --");
		DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
		Date fechaAntigua = new Date();
		Date fechaConciliacion = new Date();
		Configuracion configuracionTemporal;
		String estado = "";
		cifraControl = new CifraControl();
		ResultSet resultado;
		List<EmpresaHomologada> empresasHomologadas = new ArrayList<EmpresaHomologada>();
		EmpresaHomologada empresa = new EmpresaHomologada();
		List<LogTiendas> tiendasVirtuales = new ArrayList<LogTiendas>();
		LogTiendas tiendaVirtual = new LogTiendas();
		List<LogTiendas> tiendasRemanentes = new ArrayList<LogTiendas>();
		LogTiendas tiendaRemanente = new LogTiendas();
		List<LogTiendas> tiendasNoConciliadas = new ArrayList<LogTiendas>();
		LogTiendas tiendaNoConciliada = new LogTiendas();
		List<LogTiendas> tiendasProcesadas = new ArrayList<LogTiendas>();
		LogTiendas tiendaProcesada = new LogTiendas();
		List<DatoConciliatorio> datosConciliatorios = new ArrayList<DatoConciliatorio>();
		List<DatoConciliatorio> datosConciliatoriosNormal = new ArrayList<DatoConciliatorio>();
		try {
			escribirArchivoEstado("5|Obteniendo configuracion de la base de datos ingresos.");
			ConexionConciliacion.escribirLog(proceso, "-- 5|Obteniendo configuracion de la base de datos ingresos. --");
			if (!cargarConfiguracion()) {
				escribirArchivoEstado("2|Ocurrio un error al obtener la configuracion de la base de datos ingresos.");
				ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al obtener la configuracion de la base de datos ingresos. --");
				return "2|Ocurrio un error al obtener la configuracion.";
			}
			/***/
			interfaz = new BitacoraInterfase("1");

			if(!accesoDatos.iniciarBitacora(interfaz)) {
				ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al obtener la configuracion. --");
				return "2|Ocurrio un error al obtener la configuracion.";
			}

			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idPasoCargaConfiguracion, BitacoraInterfase.idEstatusCreacion,BitacoraInterfase.msnCargaConfiguracion, this.interfaz);
			/***/
			escribirArchivoEstado("5|Obteniendo datos de tiendas remanentes.");
			ConexionConciliacion.escribirLog(proceso, "-- 5|Obteniendo datos de tiendas remanentes. --");
			accesoDatos.conectarIngresos(false);
			resultado = accesoDatos.obtenerTiendasRemanentesIngresos();
			generarListaConciliatoria(datosConciliatorios, resultado, EnumConfiguracion.tiendaRemanente.value);
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();
			System.gc();
			/***/
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idPasoCargaConfiguracion, BitacoraInterfase.idEstatusPositivo,"", this.interfaz);



			//inicia Proceso de Informacion tiendas
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoInfoTiendas, BitacoraInterfase.idEstatusCreacion,BitacoraInterfase.msnInfoTiendas, this.interfaz);
			/***/
			escribirArchivoEstado("5|Obteniendo datos de tiendas no validas.");
			ConexionConciliacion.escribirLog(proceso, "-- 5|Obteniendo datos de tiendas no validas. --");
			accesoDatos.conectarIngresos(false);
			resultado = accesoDatos.obtenerListaTiendasVirtuales();
			while (resultado.next()) {
				tiendaVirtual = new LogTiendas();
				tiendaVirtual.setNumTienda(resultado.getInt("tienda"));
				tiendasVirtuales.add(tiendaVirtual);
			}
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();
			System.gc();

			/*
			escribirArchivoEstado("5|Creando tabla de tiendas no validas en BD Cajas.");
			accesoDatos.conectarCajas();
			if (!accesoDatos.crearTablaTiendasVirtualesCajas(tiendasVirtuales)) {
				escribirArchivoEstado(
						"2|Ocurrio un error al crear la tabla temporal de tiendas virtuales en la base de datos cajas.");
				accesoDatos.cerrarSentencia();
				accesoDatos.desconectarCajas();
				return "2|Ocurrio un error al crear tabla temporal de tiendas virtuales.";
			}
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarCajas();
			System.gc();
			*/

			accesoDatos.conectarIngresos(false);
			resultado = accesoDatos.obtenerEmpresasHomologadas();
			while (resultado.next()) {
				empresa = new EmpresaHomologada();
				empresa.setClaveCoppel(resultado.getString("clv_movimientoc"));
				empresa.setClaveBanco(resultado.getString("clv_movimientob"));
				empresa.setTipoMovimientoCoppel(resultado.getString("clv_tipomovimientoc"));
				empresa.setTipoMovimientoBanco(resultado.getString("clv_tipomovimientob"));
				empresasHomologadas.add(empresa);
			}
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();
			System.gc();

			accesoDatos.conectarIngresos(false);
			resultado = accesoDatos.obtenerTiendasRemanentesNoConciliadas();
			estado += " No conciliadas ";
			ConexionConciliacion.escribirLog(proceso, "-- llego a linea de no conciliadas --");
			while (resultado.next()) {
				tiendaRemanente = new LogTiendas();
				tiendaRemanente.setNumTienda(resultado.getInt("num_tienda"));
				tiendaRemanente.setFechaConciliacion(resultado.getDate("fecha"));
				tiendasRemanentes.add(tiendaRemanente);
			}
			ConexionConciliacion.escribirLog(proceso, "-- paso el while de resultado.next() --");
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();
			System.gc();

			/*

			accesoDatos.conectarCajas();
			resultado = accesoDatos.obtenerTiendasRemanentesCajas(empresasHomologadas, tiendasRemanentes);
			generarListaConciliatoria(datosConciliatorios, resultado, EnumConfiguracion.tiendaRemanente.value);
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarCajas();

			*/

			Collections.sort(datosConciliatorios, filtro);
			Set<DatoConciliatorio> setDatos = new LinkedHashSet<>(datosConciliatorios);
			datosConciliatorios.clear();
			datosConciliatorios.addAll(setDatos);
			calcularCifraRemanente(datosConciliatorios);
			int remanenteProcesado = 0;
			for (int i = 0; i < cifraControl.getFechaRemanente().size(); i++) {
				tiendaNoConciliada = new LogTiendas();
				tiendaNoConciliada.setNumTienda(0);
				tiendaNoConciliada.setFechaConciliacion(cifraControl.getFechaRemanente().get(i).getFechaConciliacion());
				for (int j = 0; j < cifraControl.getTiendaRemanente().size(); j++) {
					if (cifraControl.getTiendaRemanente().get(j).equals(tiendaNoConciliada)) {
						remanenteProcesado++;
					}
				}
				cifraControl.getFechaRemanente().get(i).setTotalProcesado(remanenteProcesado);
				remanenteProcesado = 0;
			}

			configuracionTemporal = new Configuracion();
			configuracionTemporal.setIduConfiguracion(EnumConfiguracion.FechaConciliacion.value);
			configuracionTemporal = configuracion.get(configuracion.indexOf(configuracionTemporal));
			fechaAntigua = dateFormat.parse(configuracionTemporal.getValor1());
			ConexionConciliacion.escribirLog(proceso, "-- fechaAntigua. => " + fechaAntigua);
			fechaConciliacion = dateFormat.parse(configuracionTemporal.getValor1());
			ConexionConciliacion.escribirLog(proceso, "-- fechaConciliacion. => " + fechaConciliacion);

			ConexionConciliacion.escribirLog(proceso, "-- 5|Obteniendo los movimientos de las tiendas. --");
			escribirArchivoEstado("5|Obteniendo los movimientos de las tiendas.");

			accesoDatos.conectarIngresos(false);
			resultado = accesoDatos.obtenerMovimientosIngresos(configuracionTemporal.getValor1());
			generarListaConciliatoria(datosConciliatoriosNormal, resultado, EnumConfiguracion.tiendaNormal.value);
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();
			accesoDatos.conectarIngresos(false);
			resultado = accesoDatos.obtenerTiendasNoConciliadasIngresos(configuracionTemporal.getValor1());
			while (resultado.next()) {
				tiendaNoConciliada = new LogTiendas();
				tiendaNoConciliada.setNumTienda(resultado.getInt("tienda"));
				tiendaNoConciliada.setFechaConciliacion(resultado.getDate("fecha"));
				tiendasNoConciliadas.add(tiendaNoConciliada);
			}
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();

			/*accesoDatos.conectarCajas();
			resultado = accesoDatos.obtenerMovimientosCajas(configuracionTemporal.getValor1(), tiendasNoConciliadas,
					empresasHomologadas);
			generarListaConciliatoria(datosConciliatoriosNormal, resultado, EnumConfiguracion.tiendaNormal.value);
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarCajas();*/

			Collections.sort(datosConciliatoriosNormal, filtro);
			escribirArchivoEstado("5|Calculando cifra de control.");
			calcularCifraNormal(datosConciliatoriosNormal);
			datosConciliatorios.addAll(datosConciliatoriosNormal);

			accesoDatos.conectarIngresos(false);
			resultado = accesoDatos.obtenerUltimaFechaPendiente();
			while (resultado.next()) {
				fechaAntigua = resultado.getDate("fec_fechalog");
			}
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();
			accesoDatos.conectarIngresos(false);

			//resultado = accesoDatos.obtenerTiendasNoConciliadasCajasCifra(fechaAntigua);
			resultado = accesoDatos.obtenerTiendasNoConciliadasIngresosCifra(fechaAntigua);
			while (resultado.next()) {
				tiendaNoConciliada = new LogTiendas();
				tiendaNoConciliada.setNumTienda(resultado.getInt("tienda"));
				tiendaNoConciliada.setFechaPendiente(fechaAntigua);
				cifraControl.getTiendaPendiente().add(tiendaNoConciliada);
				cifraControl.getfechaPendiente().add(tiendaNoConciliada);
			}
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();

			accesoDatos.conectarIngresos(false);
			//resultado = accesoDatos.obtenerTiendasNoConciliadasCajas(fechaAntigua);
			resultado = accesoDatos.obtenerTiendasNoConciliadasIngresos(fechaAntigua);
			tiendasNoConciliadas = new ArrayList<LogTiendas>();
			while (resultado.next()) {
				tiendaNoConciliada = new LogTiendas();
				tiendaNoConciliada.setNumTienda(resultado.getInt("tienda"));
				tiendaNoConciliada.setFechaConciliacion(resultado.getDate("fecha"));
				tiendasNoConciliadas.add(tiendaNoConciliada);
			}
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();

			accesoDatos.conectarIngresos(false);
			resultado = accesoDatos.obtenerTiendasProcesadasCajas(fechaConciliacion);
			tiendasProcesadas = new ArrayList<LogTiendas>();
			while (resultado.next()) {
				tiendaProcesada = new LogTiendas();
				tiendaProcesada.setTotalProcesado(resultado.getInt("totaltiendas"));
				tiendaProcesada.setFechaConciliacion(resultado.getDate("fecha"));
				tiendasProcesadas.add(tiendaProcesada);
			}
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();
			/***/
			if (datosConciliatorios.size() == 0) {
				boolean bEnvioCorreo = false;
				//bEnvioCorreo = enviarCorreo(false);
				bEnvioCorreo = tokenCorreoApi(false);
				accesoDatos.actualizarEstatusBitacora(BitacoraInterfase.idEstatusError, BitacoraInterfase.msnErrorSinRegistrosGeneraArchivo, this.interfaz);
				accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoInfoTiendas, BitacoraInterfase.idEstatusError, BitacoraInterfase.msnErrorSinRegistrosGeneraArchivo , this.interfaz);
				if (bEnvioCorreo) {
					ConexionConciliacion.escribirLog(proceso, "-- 1|No se encontraron registros para generar archivo de conciliacion. --");
					escribirArchivoEstado("1|No se encontraron registros para generar archivo de conciliacion.");
					return "1|No se encontraron movimientos para generar archivo de conciliacion.";
				} else {
					escribirArchivoEstado("1|No se encontraron registros para generar archivo de conciliacion."
							+ "y no fue posible enviar el correo electronico de notificacion");
					ConexionConciliacion.escribirLog(proceso, "-- 1|No se encontraron movimientos para generar archivo de conciliacion. y no fue posible enviar el correo electronico de notificacion --");
					return "1|No se encontraron movimientos para generar archivo de conciliacion."
							+ "y no fue posible enviar el correo electronico de notificacion";
				}
			}
			/***/

			int restantes = 0;
			for (int i = 0; i < cifraControl.getCabecera().size(); i++) {
				tiendaNoConciliada = new LogTiendas();
				tiendaNoConciliada.setNumTienda(0);
				tiendaNoConciliada.setFechaConciliacion(cifraControl.getCabecera().get(i).getDiaConciliado());

				for (int j = 0; j < tiendasProcesadas.size(); j++) {
					if (tiendasProcesadas.get(j).equals(tiendaNoConciliada)) {
						restantes = tiendasProcesadas.get(j).getTotalProcesado();
					}
				}
				cifraControl.getCabecera().get(i).setPortcentaje(((100f / (float) this.totalTiendas) * restantes));

				if (cifraControl.getCabecera().get(i).getPorcentaje() > 0
						&& cifraControl.getCabecera().get(i).getPorcentaje() < 1) {
					cifraControl.getCabecera().get(i).setPortcentaje(1);
				}

				cifraControl.getCabecera().get(i).setTiendasProcesadas(restantes);
				restantes = 0;
			}
			Set<DatoConciliatorio> setDatosConciliados = new LinkedHashSet<>(datosConciliatorios);
			datosConciliatorios.clear();
			datosConciliatorios.addAll(setDatosConciliados);
			ConexionConciliacion.escribirLog(proceso, "-- 5|Escribiendo archivo de conciliacion. --");
			escribirArchivoEstado("5|Escribiendo archivo de conciliacion.");

			ConexionConciliacion.escribirLog(proceso, "-- termina proceso de procesa informacion tiendas --");
			//termina proceso de procesa informacion tiendas
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoInfoTiendas, BitacoraInterfase.idEstatusPositivo,"", this.interfaz);

			ConexionConciliacion.escribirLog(proceso, "-- ESCRIBE ARCHIVOS LOCALES --");
			//ESCRIBE ARCHIVOS LOCALES
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEscribeArchivos, BitacoraInterfase.idEstatusCreacion,BitacoraInterfase.msnEscribeArchivos, this.interfaz);

			if (!escribirArchivoConciliacion(datosConciliatorios)) {
				accesoDatos.actualizarEstatusBitacora(BitacoraInterfase.idEstatusError, BitacoraInterfase.msnErrorEscribirArchivoConciliacion, this.interfaz);
				accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEscribeArchivos, BitacoraInterfase.idEstatusError,BitacoraInterfase.msnErrorEscribirArchivoConciliacion, this.interfaz);
				ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al escribir el archivo de conciliacion. --");
				escribirArchivoEstado("2|Ocurrio un error al escribir el archivo de conciliacion.");
				ConexionConciliacion.fh.close();
				return "2|Ocurrio un error al escribir el archivo de conciliacion.";
			}
			// HERE
			escribirArchivoEstado("5|Escribiendo archivo de conciliacion agrupado.");
			if (!escribirArchivoConciliacionAgrupado(datosConciliatorios)) {
				accesoDatos.actualizarEstatusBitacora(BitacoraInterfase.idEstatusError, BitacoraInterfase.msnErrorEscribirArchivoConciliacion, this.interfaz);
				accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEscribeArchivos, BitacoraInterfase.idEstatusError,BitacoraInterfase.msnErrorEscribirArchivoConciliacion, this.interfaz);
				ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al escribir el archivo de conciliacion. --");
				escribirArchivoEstado("2|Ocurrio un error al escribir el archivo de conciliacion.");
				ConexionConciliacion.fh.close();
				return "2|Ocurrio un error al escribir el archivo de conciliacion.";
			}
			ConexionConciliacion.escribirLog(proceso, "-- 5|Escribiendo archivo de cifras de control. --");
			escribirArchivoEstado("5|Escribiendo archivo de cifras de control.");
			if (!GenerarCifrasControl(carpetaCifras)) {
				accesoDatos.actualizarEstatusBitacora(BitacoraInterfase.idEstatusError, BitacoraInterfase.msnErrorGenerarCibrasControl, this.interfaz);
				accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEscribeArchivos, BitacoraInterfase.idEstatusError,BitacoraInterfase.msnErrorGenerarCibrasControl, this.interfaz);
				escribirArchivoEstado("2|Ocurrio un error al generar las cifras de control.");
				ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al generar las cifras de control. --");
				ConexionConciliacion.fh.close();
				return "2|Ocurrio un error al generar las cifras de control.";
			}
			ConexionConciliacion.escribirLog(proceso, "-- 5|Generando informacion para actualizar registros. --");
			escribirArchivoEstado("5|Generando informacion para actualizar registros.");
			accesoDatos.conectarIngresos(false);
			resultado = accesoDatos.obtenerTiendasConCeroMovimientos(fechaAntigua, cifraControl.getTiendaRemanente());
            if (resultado != null) {
				while (resultado.next()) {
					tiendaNoConciliada = new LogTiendas();
					tiendaNoConciliada.setFechaConciliacion(resultado.getDate("fecha"));
					tiendaNoConciliada.setNumTienda(resultado.getInt("tienda"));
					tiendaNoConciliada.setMontoTotal(0);
					cifraControl.getTiendaRemanente().add(tiendaNoConciliada);
				}
			}
			accesoDatos.cerrarSentencia();
			accesoDatos.desconectarIngresos();
			ConexionConciliacion.escribirLog(proceso, "-- termina escrito de archivos local --");
			//termina escrito de archivos local
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEscribeArchivos, BitacoraInterfase.idEstatusPositivo,"", this.interfaz);
			ConexionConciliacion.escribirLog(proceso, "-- Inicia el Envio archivos al servidor --");
			//Inicia el Envio archivos al servidor
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEnvioArchivos, BitacoraInterfase.idEstatusCreacion,BitacoraInterfase.msnEnvioArchivos, this.interfaz);

			accesoDatos.conectarIngresos(false);

			DateFormat formato = new SimpleDateFormat("yyyy-MM-dd");
			Date hoy = new Date();
			configuracionTemporal.setIduConfiguracion(EnumConfiguracion.FechaConciliacion.value);
			configuracionTemporal.setValor1(formato.format(hoy));
			ConexionConciliacion.escribirLog(proceso, "-- 5|Depositando archivo en el repositorio del connect direct. --");
			escribirArchivoEstado("5|Depositando archivo en el repositorio del connect direct.");

			//if(conectDirectSFTP == "1") {
				if (!enviarArchivoConnectSFTP(portSFTP, usuarioSFTP, contrasenaSFTP, carpetaPagos,
						rutaSFTP + carpetaOrigen, hostSFTP, privateKeySFTP)) {
					escribirArchivoEstado("2|Ocurrio un error al depositar el archivo en el servidor."
							+ " Ejecute el proceso nuevamente.");
					accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEnvioArchivos, BitacoraInterfase.idEstatusError,BitacoraInterfase.msnEnvioArchivos , this.interfaz);
					ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al depositar el archivo en el servidor. " + errorSftp + " --");
					return "2|Ocurrio un error al depositar el archivo en el servidor. " + "Ejecute el proceso nuevamente.";
			}
			//else {
			//	if (!enviarArchivoConnect(dominioConnect, usuarioConnect, contrasenaConnect, carpetaPagos,
			//			destinoConnect + carpetaOrigen)) {
			//		accesoDatos.actualizarEstatusBitacora(BitacoraInterfase.idEstatusError, BitacoraInterfase.msnErrorEnviarArchivo, this.interfaz);
			//		accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEnvioArchivos, BitacoraInterfase.idEstatusError,BitacoraInterfase.msnErrorEnviarArchivo, this.interfaz);
			//		escribirArchivoEstado("2|Ocurrio un error al depositar el archivo en el servidor."
			//				+ " Ejecute el proceso nuevamente.");
			//		ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al depositar el archivo en el servidor. " + errorConect + " --");
			//		return "2|Ocurrio un error al depositar el archivo en el servidor. " + "Ejecute el proceso nuevamente.";
			//	}
			//}
			// HERE
			// ENViA EL ARCHIVO pagoserviciocifra20170531
			String rutapagoserviciocifra = carpetaPagos;
			rutapagoserviciocifra = rutapagoserviciocifra.replace("conciliaciondetalle", "conciliacioncifra");
			String carpetaOrigenCifra = carpetaOrigen;
			carpetaOrigenCifra = carpetaOrigenCifra.replace("conciliaciondetalle", "conciliacioncifra");
			ConexionConciliacion.escribirLog(proceso, "-- conectDirectSFTP => " + conectDirectSFTP + " --");
			//if(conectDirectSFTP == "1") {
			ConexionConciliacion.escribirLog(proceso, "-- antes del metodo enviarArchivoConnectSFTP--");
				if (!enviarArchivoConnectSFTP(portSFTP, usuarioSFTP, contrasenaSFTP, rutapagoserviciocifra,
						rutaSFTP + carpetaOrigenCifra, hostSFTP, privateKeySFTP)) {
					escribirArchivoEstado("2|Ocurrio un error al depositar el archivo en el servidor."
							+ " Ejecute el proceso nuevamente.");
					accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEnvioArchivos, BitacoraInterfase.idEstatusError,"ENViA EL ARCHIVO pagoserviciocifra20170531" + errorSftp, this.interfaz);
					ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al depositar el archivo en el servidor. " + errorSftp + " --");
					return "2|Ocurrio un error al depositar el archivo en el servidor. " + "Ejecute el proceso nuevamente.";
			}
			//else {
			//	if (!enviarArchivoConnect(dominioConnect, usuarioConnect, contrasenaConnect, rutapagoserviciocifra,
			//			destinoConnect + carpetaOrigenCifra)) {
			//		accesoDatos.actualizarEstatusBitacora(BitacoraInterfase.idEstatusError, BitacoraInterfase.msnErrorEscribirArchivoConciliacion, this.interfaz);
			//		accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEnvioArchivos, BitacoraInterfase.idEstatusError,"ENViA EL ARCHIVO pagoserviciocifra20170531" + errorConect, this.interfaz);
			//		escribirArchivoEstado("2|Ocurrio un error al depositar el archivo en el servidor."
			//				+ " Ejecute el proceso nuevamente.");
			//		ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al depositar el archivo en el servidor. " + errorConect + " --");
			//		return "2|Ocurrio un error al depositar el archivo en el servidor. " + "Ejecute el proceso nuevamente.";
			//	}
			//}

			ConexionConciliacion.escribirLog(proceso, "-- Finaliza el envio de archivos al servidor --");
			//Finaliza el envio de archivos al servidor
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEnvioArchivos, BitacoraInterfase.idEstatusPositivo,"", this.interfaz);

			ConexionConciliacion.escribirLog(proceso, "-- inserta y actualiza tiendas conciliadas --");
			// inserta y actualiza tiendas conciliadas
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idInsertaTiendasConciliadas, BitacoraInterfase.idEstatusCreacion,BitacoraInterfase.msnTiendasConciliadas, this.interfaz);
			accesoDatos.conectarIngresos(false);
			ConexionConciliacion.escribirLog(proceso, "-- 5|Actualizando informacion en la base de datos. --");
			escribirArchivoEstado("5|Actualizando informacion en la base de datos.");
			if (!accesoDatos.insertarCifra(cifraControl, tiendasNoConciliadas, configuracionTemporal)) {
				accesoDatos.actualizarEstatusBitacora(BitacoraInterfase.idEstatusError, BitacoraInterfase.msnErrorActuaizarInformacion, this.interfaz);
				accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idInsertaTiendasConciliadas, BitacoraInterfase.idEstatusError,BitacoraInterfase.msnErrorActuaizarInformacion, this.interfaz);
				escribirArchivoEstado("2|Ocurrio un error al actualizar informacion en la base de datos.");
				ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al actualizar informacion en la base de datos. --");
				return "2|Ocurrio un error al actualizar los datos de las tiendas conciliadas";
			}
			ConexionConciliacion.escribirLog(proceso, "-- termina inserta y actualiza tiendas conciliadas --");
			// termina inserta y actualiza tiendas conciliadas
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idInsertaTiendasConciliadas, BitacoraInterfase.idEstatusPositivo,"", this.interfaz);
			ConexionConciliacion.escribirLog(proceso, "-- inicia envio de correo --");
			// inicia envio de correo
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEnviaCorreo, BitacoraInterfase.idEstatusCreacion,BitacoraInterfase.msnEnviaCorreo, this.interfaz);
			ConexionConciliacion.escribirLog(proceso, "-- 5|Enviando correo con archivo de cifra de control. --");
			escribirArchivoEstado("5|Enviando correo con archivo de cifra de control.");
			//if (!enviarCorreo(true)) {
			if (!tokenCorreoApi(true)) {
				accesoDatos.actualizarEstatusBitacora(BitacoraInterfase.idEstatusError, BitacoraInterfase.msnErrorEnviarCorreo, this.interfaz);
				accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEnviaCorreo, BitacoraInterfase.idEstatusError,BitacoraInterfase.msnErrorEnviarCorreo, this.interfaz);
				escribirArchivoEstado("3|Los archivos de conciliacion se generaron correctamente, "
						+ " sin embargo no se envia el correo.");
				ConexionConciliacion.escribirLog(proceso, "-- 3|Los archivos de conciliacion se generaron correctamente " + "pero no se envio el correo --");
				accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEnviaCorreo, BitacoraInterfase.idEstatusError,BitacoraInterfase.msnEnviaCorreo, this.interfaz);
				return "3|Los archivos de conciliacion se generaron correctamente " + "pero no se envio el correo";
			}
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEnviaCorreo, BitacoraInterfase.idEstatusPositivo,BitacoraInterfase.msnEnviaCorreo, this.interfaz);
			ConexionConciliacion.escribirLog(proceso, "-- 5|Eliminando archivos temporales. --");
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEliminarArchivo, BitacoraInterfase.idEstatusCreacion,BitacoraInterfase.msnEliminaArchivo, this.interfaz);
			escribirArchivoEstado("5|Eliminando archivos temporales.");
			if (!eliminarArchivos()) {
				accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEliminarArchivo, BitacoraInterfase.idEstatusError,BitacoraInterfase.msnErrorEliminarArchivos, this.interfaz);
				accesoDatos.actualizarEstatusBitacora(BitacoraInterfase.idEstatusError, BitacoraInterfase.msnErrorEliminarArchivos, this.interfaz);
				escribirArchivoEstado("1|El correo fue enviado pero ocurrio un error al" + " eliminar los archivos.");
				ConexionConciliacion.escribirLog(proceso, "-- 1|El correo fue enviado pero ocurrio un error al" + " eliminar los archivos. --");
				return "1|El correo fue enviado pero ocurrio un error al" + " eliminar los archivos.";
			}
			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEliminarArchivo, BitacoraInterfase.idEstatusPositivo,BitacoraInterfase.msnEliminaArchivo, this.interfaz);

			accesoDatos.actualizarEstatusBitacoraDetalle(interfaz.idProcesoEnviaCorreo, BitacoraInterfase.idEstatusPositivo,"", this.interfaz);
			ConexionConciliacion.escribirLog(proceso, "-- 0|El proceso de conciliacion se ejecuto correctamente. --");
			estado = "0|El proceso de conciliacion se ejecuto correctamente.";
			ConexionConciliacion.escribirLog(proceso, "--" + estado + " --");
			accesoDatos.actualizarEstatusBitacora(BitacoraInterfase.idEstatusPositivo, "Proceso exitoso", this.interfaz);

		} catch (Exception ex) {
	    	logError(ex,"generarConciliacion");
			accesoDatos.actualizarEstatusBitacora(BitacoraInterfase.idEstatusError, "Error => " + ex.getMessage() , this.interfaz);
			ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al generar la conciliacion. Favor de verificar. --");
			estado = "2|Ocurrio un error al generar la conciliacion. Favor de verificar.";
			ConexionConciliacion.escribirLog(proceso, "--" + ex.getMessage() + " --");
		}
		escribirArchivoEstado(estado);
		return estado;
	}

	/**
	 * Metodo para obtener el tokken desde la api del correo electronico.
	 *
	 * @param estado
	 *            es el tokken que ira recibiendo de la api.
	 * @return un string con el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 */
	
	private Boolean tokenCorreoApi(boolean tipoCorreo) throws ClassNotFoundException, SecurityException, NullPointerException, SQLException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso,  "-- Entro a tokenCorreoApi --");
		Boolean estado = false;
		String tokenApiKey;
		try {
			if (!isValidHost(servidorCorreo)) {
				throw new IllegalArgumentException("Host no permitido: " + servidorCorreo);
			}
			ConexionConciliacion.escribirLog(proceso, "entro al try tokenCorreoApi");
			// URL de la API a la cual quieres hacer la petición POST
			URL url = new URL("http://"+servidorCorreo+":"+puertoCorreo+pathApiTokenCorreos);
			//URL url = new URL("https://"+servidorCorreo+pathApiTokenCorreos);
			ConexionConciliacion.escribirLog(proceso,"url => " + url.toString());
			// Abrir conexión HTTP
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();

			// Configurar la conexión para el método POST
			connection.setRequestMethod("POST");
			ConexionConciliacion.escribirLog(proceso,"entro al metodo GET");
			connection.setRequestProperty("Content-Type", "application/json");
			connection.setRequestProperty("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
			ConexionConciliacion.escribirLog(proceso,"Content-Type => application/json");
			connection.setDoOutput(true);

			// Crear cuerpo de la solicitud
			String jsonInputString = "{    \"nom_usuario\": \""+usuarioApiCorreos+"\",    \"des_clave\": \""+contrasenaApiCorreos+"\"}";
			byte[] postData = jsonInputString.getBytes(StandardCharsets.UTF_8);

			int postDataLength = postData.length;
			ConexionConciliacion.escribirLog(proceso,"postDataLength : " + postDataLength);

			// Escribir datos al cuerpo de la solicitud
			try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
				ConexionConciliacion.escribirLog(proceso,"entro al try Escribir datos al cuerpo de la solicitud");
				wr.write(postData);
			}

			// Leer la respuesta del servidor
			try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
				ConexionConciliacion.escribirLog(proceso,"entro al try Leer la respuesta del servidor");
				StringBuilder response = new StringBuilder();
				String responseLine;
				while ((responseLine = br.readLine()) != null) {
					response.append(responseLine.trim());
				}
				ConexionConciliacion.escribirLog(proceso,"Respuesta del servidor:");
				ConexionConciliacion.escribirLog(proceso,response.toString());
				tokenApiKey = response.toString();
			}

			// Cerrar la conexión
			connection.disconnect();
			estado = enviarCorreoApi(tipoCorreo,tokenApiKey);
			ConexionConciliacion.escribirLog(proceso,"desconexion del server");

		} catch (Exception e) {
			logError(e,"tokenCorreoApi");
			ConexionConciliacion.escribirLog(proceso,"tokenCorreoApi Error => "+ e.getMessage());
			//e.printStackTrace();
    	}
		return estado;
	}
	/*
	/**
	 * Metodo para enviar desde la api el correo electronico.
	 *
	 * @param estado
	 *            es la cadena que ira recibiendo el estado de la operacion.
	 * @return un string con el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 */

	private Boolean enviarCorreoApi(boolean tipoCorreo,String tokenApiKey) throws ClassNotFoundException, SecurityException, NullPointerException, SQLException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso, "-- Entro a enviarCorreoApi --");
		Boolean estado = false;
		Boolean esCorrecto = tipoCorreo;
		try {
			ConexionConciliacion.escribirLog(proceso,"entro al try enviarCorreoApi");
			if (!isValidHost(servidorCorreo)) {
			    throw new IllegalArgumentException("Host no permitido: " + servidorCorreo);
			}
	        // URL de la API a la cual quieres hacer la petición POST
	        URL url = new URL("http://"+servidorCorreo+":"+puertoCorreo+pathApiCorreos);
			//URL url = new URL("https://"+servidorCorreo+pathApiTokenCorreos);
	        ConexionConciliacion.escribirLog(proceso,"url => " + url.toString());
	        // Abrir conexión HTTP
	        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
	        ConexionConciliacion.escribirLog(proceso,"carpetaCifras => " + carpetaCifras);
	     // Leer el contenido del archivo como un array de bytes
	        byte[] fileContent = readFileToByteArray(carpetaCifras);
	        ConexionConciliacion.escribirLog(proceso,"archivoDestino => fileContent");
	     // Codificar el contenido del archivo en Base64
	        String pdfBase64 = Base64.encode(fileContent);
	        // Configurar la conexión para el método POST
	        connection.setRequestMethod("POST");
	        ConexionConciliacion.escribirLog(proceso,"entro al metodo GET");
	        connection.setRequestProperty("APITOKEN", tokenApiKey);
	        connection.setRequestProperty("Content-Type", "application/json");
	        connection.setRequestProperty("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
	        ConexionConciliacion.escribirLog(proceso,"Content-Type => application/json");
	        connection.setDoOutput(true);
	        String jsonInputString;
	        // Crear cuerpo de la solicitud
	        if (esCorrecto) {
	        	ConexionConciliacion.escribirLog(proceso,"destinatario => " + correoDestinatario);
	        	ConexionConciliacion.escribirLog(proceso,"sujetoCorreo => " + sujetoCorreo);
	        	ConexionConciliacion.escribirLog(proceso,"mensajeCorreo => " + mensajeCorreo);
	        	jsonInputString = "{    \"destinatario\": \""+correoDestinatario+"\",    \"asunto\": \""+sujetoCorreo+"\",    \"html\": \"<div>"+mensajeCorreo+"</div>\",    \"archivos\": [        {            \"nombreArchivo\": \"Reporte_Conciliacion.pdf\",            \"archivo\": \""+ pdfBase64 +"\"        }     ]}";
	        }
	        else {
	        	ConexionConciliacion.escribirLog(proceso,"destinatario => " + correoDestinatario);
	        	ConexionConciliacion.escribirLog(proceso,"sujetoCorreoNegativo => " + sujetoCorreoNegativo);
	        	ConexionConciliacion.escribirLog(proceso,"mensajeCorreoNegativo => " + mensajeCorreoNegativo);
	        	jsonInputString = "{    \"destinatario\": \""+correoDestinatario+"\",    \"asunto\": \""+sujetoCorreoNegativo+"\",    \"html\": \"<div>"+mensajeCorreoNegativo+"</div>\"}";
	        }
	        byte[] postData = jsonInputString.getBytes(StandardCharsets.UTF_8);

			int postDataLength = postData.length;
			ConexionConciliacion.escribirLog(proceso,"postDataLength : " + postDataLength);
			ConexionConciliacion.escribirLog(proceso,"jsonInputString : " + jsonInputString);

	        // Escribir datos al cuerpo de la solicitud
	        try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
	        	ConexionConciliacion.escribirLog(proceso,"entro al try Escribir datos al cuerpo de la solicitud");
	            wr.write(postData);
	        }

	        // Leer la respuesta del servidor
	        try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
	        	ConexionConciliacion.escribirLog(proceso,"entro al try Leer la respuesta del servidor");
	            StringBuilder response = new StringBuilder();
	            String responseLine;
	            while ((responseLine = br.readLine()) != null) {
	                response.append(responseLine.trim());
	            }
	            ConexionConciliacion.escribirLog(proceso,"Respuesta del servidor:");
	            ConexionConciliacion.escribirLog(proceso,response.toString());
	            estado = true;
	        }

	        // Cerrar la conexión
	        connection.disconnect();

	        ConexionConciliacion.escribirLog(proceso,"desconexion del server");

		} catch (Exception e) {
	    	logError(e,"enviarCorreoApi");
	    	ConexionConciliacion.escribirLog(proceso,"error => "+ e.getMessage());
	        //e.printStackTrace();
	    	}
		return estado;
	}

	private boolean isValidHost(String host) {
	    // Aquí puedes agregar una lista blanca de hosts permitidos
	    return host.equals(host);
	}


	private static byte[] readFileToByteArray(String filePath) throws IOException, SecurityException, NullPointerException, java.io.IOException {
        File file = new File(filePath);
        file.setExecutable(false);
        file.setWritable(true);
        file.setReadable(true);

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] data = new byte[(int) file.length()];
            fis.read(data);
            return data;
        } catch (FileNotFoundException e) {
            ConexionConciliacion.escribirLog(3, "- FileNotFoundException -" + e.getMessage() + " --");
            throw e;
        } catch (java.io.IOException e) {
            ConexionConciliacion.escribirLog(3, "- IOException Read -" + e.getMessage() + " --");
            throw e;
        }
    }


	/**
	 * Metodo para enviar el correo electronico.
	 *
	 * @param estado
	 *            es la cadena que ira recibiendo el estado de la operacion.
	 * @return un string con el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 */
	@SuppressWarnings("unused")
	private Boolean enviarCorreo(boolean tipoCorreo)
			throws SecurityException, NullPointerException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo enviarCorreo --");
		Boolean estado = false;
		Boolean esCorrecto = tipoCorreo;
		final String username = usuarioCorreo;
		final String pasCorreo = contrasenaCorreo;

		Properties props = new Properties();
		props.put("mail.smtp.auth", "true");
		//props.put("mail.smtp.starttls.enable", "true");
		props.put("mail.smtp.host", servidorCorreo);
		props.put("mail.smtp.port", puertoCorreo);

		Session session = Session.getInstance(props, new javax.mail.Authenticator() {
			protected PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(username, pasCorreo);
			}
		});

		try {

			MimeMessage message = new MimeMessage(session);
			message.setFrom(new InternetAddress(correoOrigen));
			message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(correoDestinatario));
			BodyPart messageBodyPart = new MimeBodyPart();

			if (esCorrecto) {
				message.setSubject(sujetoCorreo, "UTF-8");
				messageBodyPart.setText(mensajeCorreo);
				Multipart multipart = new MimeMultipart();
				multipart.addBodyPart(messageBodyPart);

				messageBodyPart = new MimeBodyPart();
				String filename1 = archivoDestino;
				DataSource source1 = new FileDataSource(carpetaCifras);
				messageBodyPart.setDataHandler(new DataHandler(source1));
				messageBodyPart.setFileName(filename1);
				multipart.addBodyPart(messageBodyPart);
				message.setContent(multipart);
			} else {
				message.setSubject(sujetoCorreoNegativo);
				messageBodyPart.setText(mensajeCorreoNegativo);
				Multipart multipart = new MimeMultipart();
				multipart.addBodyPart(messageBodyPart);
				message.setContent(multipart);
			}

			Transport.send(message);
			estado = true;
		} catch (MessagingException e) {
			e.getMessage();
			ConexionConciliacion.escribirLog(proceso, "--" + e.getMessage() + " --");
		}
		return estado;
	}

	/**
	 * Metodo para calcular las tiendas remanentes procesadas para la cifra de
	 * control.
	 *
	 * @param datoConciliatorio
	 *            lista que contiene la lista de los movimientos remanentes con
	 *            la cual se calcular la cifra de control.
	 * @return un string indicando el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 */
	private Boolean calcularCifraRemanente(List<DatoConciliatorio> datoConciliatorio)
			throws SecurityException, NullPointerException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo calcularCifraRemanente --");
		LogTiendas tiendaRemanente;
		DatoConciliatorio dato;
		Boolean estado = false;
		DateFormat fechaFormato = new SimpleDateFormat("yyyy-MM-dd");
		String fechaComparar = null;
		String ultimaFecha = null;
		int ultimaTienda = 0;
		int montoTiendaRemanente = 0;
		for (int i = 0; i <= datoConciliatorio.size(); i++) {
			if (i == datoConciliatorio.size()) {
				dato = new DatoConciliatorio();
			} else {
				dato = datoConciliatorio.get(i);
			}

			fechaComparar = fechaFormato.format(dato.getFechaMovimiento());
			if (ultimaTienda != dato.getNumTienda()) {
				if (ultimaTienda != 0) {
					tiendaRemanente = new LogTiendas();
					tiendaRemanente.setNumTienda(datoConciliatorio.get(i - 1).getNumTienda());
					tiendaRemanente.setFechaConciliacion(datoConciliatorio.get(i - 1).getFechaMovimiento());
					tiendaRemanente.setMontoTotal(montoTiendaRemanente / 100);
					cifraControl.getTiendaRemanente().add(tiendaRemanente);
					montoTiendaRemanente = 0;
				}
				montoTiendaRemanente += Integer.parseInt(dato.getImporte());
			} else {
				montoTiendaRemanente += Integer.parseInt(dato.getImporte());
			}
			if (!Objects.equals(ultimaFecha, fechaComparar)) {
				if (ultimaFecha != null) {
					tiendaRemanente = new LogTiendas();
					tiendaRemanente.setTotalProcesado(totalTiendasRemanentes);
					tiendaRemanente.setFechaConciliacion(datoConciliatorio.get(i - 1).getFechaMovimiento());
					tiendaRemanente.setMontoTotal(montoTotalRemanente / 100);
					cifraControl.getFechaRemanente().add(tiendaRemanente);
					montoTotalRemanente = 0;
				}
			}
			montoTotalRemanente += Integer.parseInt(dato.getImporte());
			ultimaFecha = fechaFormato.format(dato.getFechaMovimiento());
			ultimaTienda = dato.getNumTienda();
		}
		estado = true;
		System.gc();
		return estado;
	}

	/**
	 * Metodo para calcular las tiendas normales procesadas para la cifra de
	 * control.
	 *
	 * @param datosConciliatorios
	 *            lista que contiene la lista de los movimientos con la cual se
	 *            calculara la cifra de controls.
	 * @return un string indicando el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 */
	private Boolean calcularCifraNormal(List<DatoConciliatorio> datosConciliatorios)
			throws SecurityException, NullPointerException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo calcularCifraNormal --");
		CabeceraCifra cabecera;
		DatoConciliatorio dato;
		Boolean estado = false;
		DateFormat fechaFormato = new SimpleDateFormat("yyyy-MM-dd");
		String ultimaFecha = null;
		String fechaComparar = null;
		for (int i = 0; i <= datosConciliatorios.size(); i++) {
			if (i == datosConciliatorios.size()) {
				dato = new DatoConciliatorio();
			} else {
				dato = datosConciliatorios.get(i);

			}
			fechaComparar = fechaFormato.format(dato.getFechaMovimiento());

			if (!Objects.equals(ultimaFecha, fechaComparar)) {
				if (ultimaFecha != null) {
					cabecera = new CabeceraCifra();
					cabecera.setMontoDelDia(montoTotal / 100);
					cabecera.setDiaConciliado(datosConciliatorios.get(i - 1).getFechaMovimiento());
					cifraControl.getCabecera().add(cabecera);
					montoTotal = 0;
				}
			}
			montoTotal += Integer.parseInt(dato.getImporte());
			ultimaFecha = fechaFormato.format(dato.getFechaMovimiento());
		}
		System.gc();
		estado = true;
		return estado;
	}

	/**
	 * Instancia de un nuevo Comparator para filtrar la lista de movimientos por
	 * fecha y numero de tienda.
	 */
	private Comparator<DatoConciliatorio> filtro = (dato1, dato2) -> {
		Date fecha = dato1.getFechaMovimiento();
		Date fechaComparar = dato2.getFechaMovimiento();
		int resultadoComparacion = fecha.compareTo(fechaComparar);

		if (resultadoComparacion != 0) {
			return resultadoComparacion;
		} else {
			Integer numTienda = dato1.getNumTienda();
			Integer numTiendaComparar = dato2.getNumTienda();
			return numTienda.compareTo(numTiendaComparar);
		}
	};

	/**
	 * M�todo para enviar el archivo de pagos de servicio al connect direct.
	 *
	 * @param dominio
	 *            dominio de la ruta del servidor.
	 * @param usuario
	 *            usuario para la autentificaci�n en el connect direct.
	 * @param clvstring
	 *            contrasena del usuario connect direct.
	 * @param rutaOrigen
	 *            ruta local donde se encuentra el archivo.
	 * @param rutaDestino
	 *            ruta destino del servidor para el connect direct.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 * @throws IOException
	 * @throws java.io.IOException
	 */
	public Boolean enviarArchivoConnect(final String dominio, final String usuario, final String clvstring,
			final String rutaOrigen, final String rutaDestino)
			throws SecurityException, NullPointerException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo enviarArchivoConnect --");
		Boolean estado = false;
		final NtlmPasswordAuthentication autentificacion = new NtlmPasswordAuthentication(dominio, usuario, clvstring);
		try {
			final SmbFile archivoFinal = new SmbFile(rutaDestino, autentificacion);
			try (final SmbFileOutputStream flujoSalida = new SmbFileOutputStream(archivoFinal);
				 final FileInputStream flujoEntrada = new FileInputStream(new File(rutaOrigen))) {

				final byte[] buf = new byte[16 * 1024 * 1024];
				int len;

				while ((len = flujoEntrada.read(buf)) > 0) {
					flujoSalida.write(buf, 0, len);
				}
				estado = true;
			}
		} catch (IOException | java.io.IOException ex) {
			//ex.printStackTrace();
			errorConect = ex.getMessage();
			ConexionConciliacion.escribirLog(proceso, "--" + ex.getMessage() + " --");
		}
		return estado;
	}

	public boolean enviarArchivoConnectSFTP(final String portSFTP, final String usuarioSFTP, final String contrasenaSFTP,
            final String rutaOrigen, final String rutaSFTP, final String hostSFTP, final String privateKeySFTP)
                    throws SecurityException,JSchException,SftpException, NullPointerException, java.io.IOException, JSchException, SftpException {
        int idx = rutaOrigen.lastIndexOf("/");  //  obtiene posición de la última /  para calcular el filename
        ConexionConciliacion.escribirLog(proceso, "--Entra al metodo enviarArchivoConnectSFTP --");
        String filename = "" ;
        // if file is in folder(s), create them first
        if(idx > -1) {
            filename = rutaOrigen.substring(idx + 1, rutaOrigen.length());
        } // if
        else {
            filename = rutaOrigen;
        } // else
        ConexionConciliacion.escribirLog(proceso, "-- filename --" + filename);
        String localPath = rutaOrigen.split("/")[0].concat("/").concat(rutaOrigen.split("/")[1]).concat("/").concat(rutaOrigen.split("/")[2]).concat("/").concat(rutaOrigen.split("/")[3]);
        //String sftpPath = "/bcpl-cpl/servicioscruzados/Conciliacion";
        ConexionConciliacion.escribirLog(proceso, "-- rutaOrigen --" + rutaOrigen);
        ConexionConciliacion.escribirLog(proceso, "-- rutaSFTP --" + rutaSFTP);
        String sftpPath = rutaSFTP;
        String sftpHost = hostSFTP;
        String sftpUser = usuarioSFTP;
        String sftpp = contrasenaSFTP;
        String privateKey = privateKeySFTP;

        Boolean estado = false;

        try {
        	ConexionConciliacion.escribirLog(proceso, "--Entra al try de enviarArchivoConnectSFTP --");
            JSch jsch = new JSch();
            ConexionConciliacion.escribirLog(proceso, "--privateKey --" + privateKey);
            //jsch.addIdentity(privateKey);
            ConexionConciliacion.escribirLog(proceso, "--despues de addIdentity --");
            ConexionConciliacion.escribirLog(proceso, "-- Sesion --");
            com.jcraft.jsch.Session session = jsch.getSession(sftpUser, sftpHost, Integer.parseInt(portSFTP));
            ConexionConciliacion.escribirLog(proceso, "-- Despues de getSesion --");
            session.setConfig("StrictHostKeyChecking", "no");
            ConexionConciliacion.escribirLog(proceso, "-- Estableciendo Contraseña --");
            session.setPassword(sftpp);
            ConexionConciliacion.escribirLog(proceso, "--Connecting------ --");
            session.connect();
            ConexionConciliacion.escribirLog(proceso, "--Established Session------ --");

            Channel channel = session.openChannel("sftp");
            ChannelSftp sftpChannel = (ChannelSftp) channel;
            sftpChannel.connect();
            ConexionConciliacion.escribirLog(proceso, "--Opened sftp Channel--");
            ConexionConciliacion.escribirLog(proceso, "--Copying file to Host--");
            ConexionConciliacion.escribirLog(proceso, "-- path --" + sftpPath);
            ConexionConciliacion.escribirLog(proceso, "-- localPath --" + localPath);
            sftpChannel.put(localPath + "/" + filename, sftpPath);
            ConexionConciliacion.escribirLog(proceso, "--Copied file to Host--");
         // Cambiar permisos del archivo remoto
            sftpChannel.chmod(755, sftpPath);
            ConexionConciliacion.escribirLog(proceso, "--Permissions Changed--");
            sftpChannel.disconnect();
            session.disconnect();
            ConexionConciliacion.escribirLog(proceso, "--Disconnected from sftp--");
            estado = true;
        } // try
        catch (IOException | java.io.IOException ex) {
        	errorSftp = ex.getMessage();
            ConexionConciliacion.escribirLog(proceso,"--"+ ex.getMessage() +" --");
        } // catch
        ConexionConciliacion.escribirLog(proceso, "-- finalizo proceso enviarArchivoConnectSFTP--");
        return estado;
    } // enviarArchivoConnectSFTP

	private static void logError(Exception e,String nombre) throws SecurityException, NullPointerException, java.io.IOException{
		ConexionConciliacion.escribirLog(2,"error => " + nombre + e.getMessage());
    	StringWriter sw = new StringWriter();
    	@SuppressWarnings("unused")
		PrintWriter pw = new PrintWriter(sw);
        //e.printStackTrace(pw);
        String trace = sw.toString();
        ConexionConciliacion.escribirLog(2,"trace => "+ nombre + trace);
	}

	/**
	 * Metodo para reenviar el acrhvio al connect direct
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 */
	public String reenviarArchivoConnect() throws SecurityException,JSchException,SftpException, NullPointerException, java.io.IOException, JSchException, SftpException, ClassNotFoundException, SQLException {
		String estado = null;
		try {
			if (!cargarConfiguracion()) {
				escribirArchivoEstado("2|Ocurrio un error al obtener la configuracion de la base de datos ingresos.");
				return "2|Ocurrio un error al obtener la configuracion.";
			}

			//if(conectDirectSFTP == "1") {
				if (!enviarArchivoConnectSFTP(portSFTP, usuarioSFTP, contrasenaSFTP, carpetaPagos,
						rutaSFTP + carpetaOrigen, hostSFTP, privateKeySFTP)) {
					escribirArchivoEstado(
							"2|Ocurrio un error al depositar el archivo  " + "en el repositorio del connect direct.");
					ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al depositar el archivo  " + "en el repositorio del connect direct.--");
					return "2|Ocurrio un error al depositar el archivo en el servidor del connect direct.";
				}
			//}
			//else {
				/*
				 * if (!enviarArchivoConnect(dominioConnect, usuarioConnect, contrasenaConnect, carpetaPagos,
						destinoConnect + carpetaOrigen)) {
					escribirArchivoEstado(
							"2|Ocurri� un error al depositar el archivo  " + "en el repositorio del connect direct.");
					ConexionConciliacion.escribirLog(proceso, "-- 2|Ocurrio un error al depositar el archivo  " + "en el repositorio del connect direct.--");
					return "2|Ocurri� un error al depositar el archivo en el servidor del connect direct.";
				}
				*/
			//}
				ConexionConciliacion.escribirLog(proceso, "-- 0|Enviando correo con archivo de cifra de control. --");
			escribirArchivoEstado("0|Enviando correo con archivo de cifra de control.");
			//if (!enviarCorreo(true)) {
			if (!tokenCorreoApi(true)) {
				escribirArchivoEstado(
						"3|Los archivos de conciliacion se generaron correctamente " + "pero no se envio el correo");
				ConexionConciliacion.escribirLog(proceso, "-- 3|Los archivos de conciliacion se generaron correctamente " + "pero no se envio el correo --");
				return "3|Los archivos de conciliacion se generaron correctamente " + "pero no se envio el correo";
			}

			escribirArchivoEstado(
					"0|El correo fue enviado pero Ocurrio un error al eliminar los archivos, favor de verificar manualmente.");
			ConexionConciliacion.escribirLog(proceso, "-- 0|El correo fue enviado pero Ocurrio un error al eliminar los archivos, favor de verificar manualmente. --");
			if (!eliminarArchivos()) {
				escribirArchivoEstado("1|El correo fue enviado pero Ocurrio un error al"
						+ " eliminar los archivos, favor de verificar manualmente");
				ConexionConciliacion.escribirLog(proceso, "-- 1|El correo fue enviado pero Ocurrio un error al eliminar los archivos, favor de verificar manualmente --");
				return "1|El correo fue enviado pero Ocurrio un error al"
						+ " eliminar los archivos, favor de verificar manualmente";
			}
			ConexionConciliacion.escribirLog(proceso, "-- 0| El proceso de conciliacion se ejecuto correctamente. --");
			estado = "0| El proceso de conciliacion se ejecuto correctamente.";
			ConexionConciliacion.escribirLog(proceso, "--" + estado + " --");
		} catch (Exception ex) {
			ex.getMessage();
			ConexionConciliacion.escribirLog(proceso, "--" + ex.getMessage() + " --");
		}
		return estado;
	}

	/**
	 * Metodo que invoca el webservice para reenviar el correo, con la cifra de
	 * control.
	 *
	 * @return un string indicando el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 */
	public String reenviarCorreo() throws SecurityException, NullPointerException, java.io.IOException, ClassNotFoundException, SQLException {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo reenviarCorreo --");
		String estado = "0|El correo fue enviado correctamente.";
		if (!cargarConfiguracion()) {
			estado = "3|Ocurrio un error al obtener la configuracion de la base de datos ingresos.";
		} else {
			//if (!enviarCorreo(true)) {
			if (!tokenCorreoApi(true)) {
				estado = "3|Ocurrio un error al enviar el correo.";
			}

			else {
				if (!eliminarArchivos()) {
					estado = "1|El correo fue enviado pero ocurrio un error al"
							+ " eliminar los archivos, favor de verificar manualmente";
				}
			}

		}
		return estado;
	}

	/**
	 * Metodo invocado por el webservice para eliminar archivos.
	 *
	 * @return un string indicando el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 * @throws SecurityException
	 */
	public String eliminacionArchivos() throws SecurityException, NullPointerException, java.io.IOException {
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo eliminacionArchivos --");
		String estado = "0|Archivos eliminados correctamente.";
		if (!cargarConfiguracion()) {
			estado = "1|Ocurrio un error al  eliminar los archivos.";
		}
		if (!eliminarArchivos()) {
			estado = "1|Ocurrio un error al  eliminar los archivos.";
		}
		return estado;
	}

	/**
	 * Metodo para eliminar los archivos creados en el servidor, cifra y
	 * pagadetalles.
	 *
	 * @return un valor boolean indicando si la operacion fue exitosa.
	 * @throws java.io.IOException
	 * @throws NullPointerException
	 */
	public Boolean eliminarArchivos() throws NullPointerException, java.io.IOException {
		Boolean estado = false;
		File archivoPago = new File(carpetaPagos);
		File archivoCifras = new File(carpetaCifras);

		archivoPago.setExecutable(false);
		archivoPago.setReadable(true);
		archivoPago.setWritable(true);

		archivoCifras.setExecutable(false);
		archivoCifras.setReadable(true);
		archivoCifras.setWritable(true);

		try {
			// Se intenta eliminar el archivo de pagos
			if (archivoPago.exists()) {
				archivoPago.delete();
				// Se verifica si el archivo fue eliminado correctamente
				if (!archivoPago.exists()) {
					ConexionConciliacion.escribirLog(proceso,"-- El archivo de pagos fue eliminado correctamente.");
				} else {
					ConexionConciliacion.escribirLog(proceso,"-- No se pudo eliminar el archivo de pagos..");
				}
			} else {
				ConexionConciliacion.escribirLog(proceso,"-- El archivo de pagos no existe.");
			}

			// Se intenta eliminar el archivo de cifras
			if (archivoCifras.exists()) {
				archivoCifras.delete();
				// Se verifica si el archivo fue eliminado correctamente
				if (!archivoCifras.exists()) {
					ConexionConciliacion.escribirLog(proceso,"-- El archivo de cifras fue eliminado correctamente.");
				} else {
					ConexionConciliacion.escribirLog(proceso,"-- No se pudo eliminar el archivo de cifras.");
				}
			} else {
				ConexionConciliacion.escribirLog(proceso,"-- El archivo de cifras no existe.");
			}

			// Si ambos archivos fueron eliminados correctamente, se actualiza el estado a true
			if (!archivoPago.exists() && !archivoCifras.exists()) {
				estado = true;
			}
		} catch (Exception e) {
	    	logError(e," eliminarArchivos");
			ConexionConciliacion.escribirLog(proceso,"-- Se produjo una excepción de seguridad al intentar eliminar los archivos");
		}

		return estado;
	}

	/**
	 * Metodo para eliminar los archivos creados en el servidor, cifra y
	 * pagadetalles.
	 *
	 * @return un valor boolean indicando si la operacion fue exitosa.
	 * @throws java.io.IOException
	 */
	public String consultarEstado() throws java.io.IOException {
		System.out.println("debug consultarestado");
		String estado = null;
		if (Utilidades.checkAuthorization(Constantes.AUTHORIZATION)) {
			try (BufferedReader br = new BufferedReader(
					new InputStreamReader(new FileInputStream(ConexionConciliacion.rutaEstatus), "UTF-8"))) {
				StringBuilder informacion = new StringBuilder();
				String linea = br.readLine();

				while (linea != null) {
					informacion.append(linea);
					informacion.append(System.lineSeparator());
					linea = br.readLine();
				}
				estado = informacion.toString();
			}
		}
		return estado;
	}
}
