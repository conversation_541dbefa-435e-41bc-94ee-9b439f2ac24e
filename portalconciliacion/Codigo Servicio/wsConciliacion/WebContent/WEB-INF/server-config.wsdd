<ns1:deployment xmlns="http://xml.apache.org/axis/wsdd/" xmlns:java="http://xml.apache.org/axis/wsdd/providers/java" xmlns:ns1="http://xml.apache.org/axis/wsdd/">
 <ns1:globalConfiguration>
  <ns1:parameter name="sendMultiRefs" value="true"/>
  <ns1:parameter name="disablePrettyXML" value="true"/>
  <ns1:parameter name="adminPassword" value="admin"/>
  <ns1:parameter name="attachments.Directory" value="C:\Users\<USER>\workspace\.metadata\.plugins\org.eclipse.wst.server.core\tmp0\wtpwebapps\ws-conciliacion\WEB-INF\attachments"/>
  <ns1:parameter name="dotNetSoapEncFix" value="true"/>
  <ns1:parameter name="enableNamespacePrefixOptimization" value="false"/>
  <ns1:parameter name="sendXMLDeclaration" value="true"/>
  <ns1:parameter name="sendXsiTypes" value="true"/>
  <ns1:parameter name="attachments.implementation" value="org.apache.axis.attachments.AttachmentsImpl"/>
  <ns1:requestFlow>
   <ns1:handler type="java:org.apache.axis.handlers.JWSHandler">
    <ns1:parameter name="scope" value="session"/>
   </ns1:handler>
   <ns1:handler type="java:org.apache.axis.handlers.JWSHandler">
    <ns1:parameter name="scope" value="request"/>
    <ns1:parameter name="extension" value=".jwr"/>
   </ns1:handler>
  </ns1:requestFlow>
 </ns1:globalConfiguration>
 <ns1:handler name="URLMapper" type="java:org.apache.axis.handlers.http.URLMapper"/>
 <ns1:handler name="LocalResponder" type="java:org.apache.axis.transport.local.LocalResponder"/>
 <ns1:handler name="Authenticate" type="java:org.apache.axis.handlers.SimpleAuthenticationHandler"/>
 <ns1:service name="AdminService" provider="java:MSG">
  <ns1:parameter name="allowedMethods" value="AdminService"/>
  <ns1:parameter name="enableRemoteAdmin" value="false"/>
  <ns1:parameter name="className" value="org.apache.axis.utils.Admin"/>
  <ns1:namespace>http://xml.apache.org/axis/wsdd/</ns1:namespace>
 </ns1:service>
 <ns1:service name="WsConciliacion" provider="java:RPC" style="wrapped" use="literal">
  <ns2:operation name="reenviarCorreo" qname="ns1:reenviarCorreo" returnQName="ns1:reenviarCorreoReturn" returnType="xsd:string" soapAction="" xmlns:ns1="http://servicioweb.wsconciliacion.coppel.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ns2="http://xml.apache.org/axis/wsdd/">
   <ns2:parameter qname="ns1:peticion" type="xsd:int"/>
  </ns2:operation>
  <ns1:operation name="consultarEstadoConciliacion" qname="ns2:consultarEstadoConciliacion" returnQName="ns2:consultarEstadoConciliacionReturn" returnType="xsd:string" soapAction="" xmlns:ns2="http://servicioweb.wsconciliacion.coppel.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  <ns1:operation name="generarConciliacion" qname="ns3:generarConciliacion" returnQName="ns3:generarConciliacionReturn" returnType="xsd:string" soapAction="" xmlns:ns3="http://servicioweb.wsconciliacion.coppel.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  <ns1:operation name="obtenerUltimaFecha" qname="ns4:obtenerUltimaFecha" returnQName="ns4:obtenerUltimaFechaReturn" returnType="xsd:string" soapAction="" xmlns:ns4="http://servicioweb.wsconciliacion.coppel.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  <ns1:parameter name="allowedMethods" value="generarConciliacion reenviarCorreo obtenerUltimaFecha consultarEstadoConciliacion"/>
  <ns1:parameter name="typeMappingVersion" value="1.2"/>
  <ns1:parameter name="wsdlPortType" value="WsConciliacion"/>
  <ns1:parameter name="className" value="com.coppel.wsconciliacion.servicioweb.WsConciliacion"/>
  <ns1:parameter name="wsdlServicePort" value="WsConciliacion"/>
  <ns1:parameter name="schemaQualified" value="http://servicioweb.wsconciliacion.coppel.com"/>
  <ns1:parameter name="wsdlTargetNamespace" value="http://servicioweb.wsconciliacion.coppel.com"/>
  <ns1:parameter name="wsdlServiceElement" value="WsConciliacionService"/>
 </ns1:service>
 <ns1:service name="Version" provider="java:RPC">
  <ns1:parameter name="allowedMethods" value="getVersion"/>
  <ns1:parameter name="className" value="org.apache.axis.Version"/>
 </ns1:service>
 <ns1:transport name="http">
  <ns1:requestFlow>
   <ns1:handler type="URLMapper"/>
   <ns1:handler type="java:org.apache.axis.handlers.http.HTTPAuthHandler"/>
  </ns1:requestFlow>
  <ns1:parameter name="qs:list" value="org.apache.axis.transport.http.QSListHandler"/>
  <ns1:parameter name="qs:wsdl" value="org.apache.axis.transport.http.QSWSDLHandler"/>
  <ns1:parameter name="qs.list" value="org.apache.axis.transport.http.QSListHandler"/>
  <ns1:parameter name="qs.method" value="org.apache.axis.transport.http.QSMethodHandler"/>
  <ns1:parameter name="qs:method" value="org.apache.axis.transport.http.QSMethodHandler"/>
  <ns1:parameter name="qs.wsdl" value="org.apache.axis.transport.http.QSWSDLHandler"/>
 </ns1:transport>
 <ns1:transport name="local">
  <ns1:responseFlow>
   <ns1:handler type="LocalResponder"/>
  </ns1:responseFlow>
 </ns1:transport>
</ns1:deployment>