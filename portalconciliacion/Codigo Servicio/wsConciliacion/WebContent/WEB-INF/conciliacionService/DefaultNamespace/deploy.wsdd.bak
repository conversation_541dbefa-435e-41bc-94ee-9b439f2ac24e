<!-- Use this file to deploy some handlers/chains and services      -->
<!-- Two ways to do this:                                           -->
<!--   java org.apache.axis.client.AdminClient deploy.wsdd          -->
<!--      after the axis server is running                          -->
<!-- or                                                             -->
<!--   java org.apache.axis.utils.Admin client|server deploy.wsdd   -->
<!--      from the same directory that the Axis engine runs         -->

<deployment
    xmlns="http://xml.apache.org/axis/wsdd/"
    xmlns:java="http://xml.apache.org/axis/wsdd/providers/java">

  <!-- Services from conciliacionService WSDL service -->

  <service name="conciliacion" provider="java:RPC" style="document" use="literal">
      <parameter name="wsdlTargetNamespace" value="http://DefaultNamespace"/>
      <parameter name="wsdlServiceElement" value="conciliacionService"/>
      <parameter name="schemaQualified" value="http://DefaultNamespace"/>
      <parameter name="wsdlServicePort" value="conciliacion"/>
      <parameter name="className" value="DefaultNamespace.ConciliacionSoapBindingImpl"/>
      <parameter name="wsdlPortType" value="conciliacion"/>
      <parameter name="typeMappingVersion" value="1.2"/>
      <operation name="sum" qname="operNS:sum" xmlns:operNS="http://DefaultNamespace" returnQName="retNS:sumReturn" xmlns:retNS="http://DefaultNamespace" returnType="rtns:float" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <parameter qname="pns:valor" xmlns:pns="http://DefaultNamespace" type="tns:float" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <operation name="mul" qname="operNS:mul" xmlns:operNS="http://DefaultNamespace" returnQName="retNS:mulReturn" xmlns:retNS="http://DefaultNamespace" returnType="rtns:float" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <parameter qname="pns:valor" xmlns:pns="http://DefaultNamespace" type="tns:float" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <operation name="resta" qname="operNS:resta" xmlns:operNS="http://DefaultNamespace" returnQName="retNS:restaReturn" xmlns:retNS="http://DefaultNamespace" returnType="rtns:float" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <parameter qname="pns:valor" xmlns:pns="http://DefaultNamespace" type="tns:float" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <operation name="generarConciliacion" qname="operNS:generarConciliacion" xmlns:operNS="http://DefaultNamespace" returnQName="retNS:generarConciliacionReturn" xmlns:retNS="http://DefaultNamespace" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
      </operation>
      <operation name="generateCifrasControl" qname="operNS:GenerateCifrasControl" xmlns:operNS="http://DefaultNamespace" returnQName="retNS:GenerateCifrasControlReturn" xmlns:retNS="http://DefaultNamespace" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <parameter qname="pns:ruta" xmlns:pns="http://DefaultNamespace" type="tns:string" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <operation name="generarPdf" qname="operNS:generarPdf" xmlns:operNS="http://DefaultNamespace" soapAction="" >
        <fault name="IOException" qname="fns:fault" xmlns:fns="http://DefaultNamespace" class="DefaultNamespace.IOException" type="tns:anyType" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <operation name="createPdf" qname="operNS:createPdf" xmlns:operNS="http://DefaultNamespace" soapAction="" >
        <parameter qname="pns:dest" xmlns:pns="http://DefaultNamespace" type="tns:string" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
        <fault name="IOException" qname="fns:fault" xmlns:fns="http://DefaultNamespace" class="DefaultNamespace.IOException" type="tns:anyType" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <parameter name="allowedMethods" value="generarPdf mul generarConciliacion sum resta generateCifrasControl createPdf"/>

      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>GenerateCifrasControlResponse"
        type="java:DefaultNamespace.GenerateCifrasControlResponse"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>GenerateCifrasControl"
        type="java:DefaultNamespace.GenerateCifrasControl"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>mulResponse"
        type="java:DefaultNamespace.MulResponse"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>resta"
        type="java:DefaultNamespace.Resta"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>createPdf"
        type="java:DefaultNamespace.CreatePdf"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>sumResponse"
        type="java:DefaultNamespace.SumResponse"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>sum"
        type="java:DefaultNamespace.Sum"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>createPdfResponse"
        type="java:DefaultNamespace.CreatePdfResponse"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>generarConciliacion"
        type="java:DefaultNamespace.GenerarConciliacion"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>generarConciliacionResponse"
        type="java:DefaultNamespace.GenerarConciliacionResponse"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>mul"
        type="java:DefaultNamespace.Mul"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>generarPdf"
        type="java:DefaultNamespace.GenerarPdf"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>generarPdfResponse"
        type="java:DefaultNamespace.GenerarPdfResponse"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>restaResponse"
        type="java:DefaultNamespace.RestaResponse"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
  </service>
</deployment>
