<?xml version="1.0" encoding="UTF-8"?><!-- Use this file to deploy some handlers/chains and services      --><!-- Two ways to do this:                                           --><!--   java org.apache.axis.client.AdminClient deploy.wsdd          --><!--      after the axis server is running                          --><!-- or                                                             --><!--   java org.apache.axis.utils.Admin client|server deploy.wsdd   --><!--      from the same directory that the Axis engine runs         --><deployment xmlns="http://xml.apache.org/axis/wsdd/" xmlns:java="http://xml.apache.org/axis/wsdd/providers/java">

  <!-- Services from conciliacionService WSDL service -->

  <service name="conciliacion" provider="java:RPC" style="document" use="literal">
      <parameter name="wsdlTargetNamespace" value="http://DefaultNamespace"/>
      <parameter name="wsdlServiceElement" value="conciliacionService"/>
      <parameter name="schemaQualified" value="http://DefaultNamespace"/>
      <parameter name="wsdlServicePort" value="conciliacion"/>
      <parameter name="className" value="conciliacion"/>
      <parameter name="wsdlPortType" value="conciliacion"/>
      <parameter name="typeMappingVersion" value="1.2"/>
      <operation xmlns:operNS="http://DefaultNamespace" xmlns:retNS="http://DefaultNamespace" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="sum" qname="operNS:sum" returnQName="retNS:sumReturn" returnType="rtns:float" soapAction="">
        <parameter xmlns:pns="http://DefaultNamespace" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:valor" type="tns:float"/>
      </operation>
      <operation xmlns:operNS="http://DefaultNamespace" xmlns:retNS="http://DefaultNamespace" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="mul" qname="operNS:mul" returnQName="retNS:mulReturn" returnType="rtns:float" soapAction="">
        <parameter xmlns:pns="http://DefaultNamespace" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:valor" type="tns:float"/>
      </operation>
      <operation xmlns:operNS="http://DefaultNamespace" xmlns:retNS="http://DefaultNamespace" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="resta" qname="operNS:resta" returnQName="retNS:restaReturn" returnType="rtns:float" soapAction="">
        <parameter xmlns:pns="http://DefaultNamespace" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:valor" type="tns:float"/>
      </operation>
      <operation xmlns:operNS="http://DefaultNamespace" xmlns:retNS="http://DefaultNamespace" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="generarConciliacion" qname="operNS:generarConciliacion" returnQName="retNS:generarConciliacionReturn" returnType="rtns:string" soapAction="">
      </operation>
      <operation xmlns:operNS="http://DefaultNamespace" xmlns:retNS="http://DefaultNamespace" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="generateCifrasControl" qname="operNS:GenerateCifrasControl" returnQName="retNS:GenerateCifrasControlReturn" returnType="rtns:string" soapAction="">
        <parameter xmlns:pns="http://DefaultNamespace" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:ruta" type="tns:string"/>
      </operation>
      <operation xmlns:operNS="http://DefaultNamespace" name="generarPdf" qname="operNS:generarPdf" soapAction="">
        <fault xmlns:fns="http://DefaultNamespace" xmlns:tns="http://www.w3.org/2001/XMLSchema" name="IOException" qname="fns:fault" class="DefaultNamespace.IOException" type="tns:anyType"/>
      </operation>
      <operation xmlns:operNS="http://DefaultNamespace" name="createPdf" qname="operNS:createPdf" soapAction="">
        <parameter xmlns:pns="http://DefaultNamespace" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:dest" type="tns:string"/>
        <fault xmlns:fns="http://DefaultNamespace" xmlns:tns="http://www.w3.org/2001/XMLSchema" name="IOException" qname="fns:fault" class="DefaultNamespace.IOException" type="tns:anyType"/>
      </operation>
      <parameter name="allowedMethods" value="generarPdf mul generarConciliacion sum resta generateCifrasControl createPdf"/>

      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;GenerateCifrasControlResponse" type="java:DefaultNamespace.GenerateCifrasControlResponse" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;GenerateCifrasControl" type="java:DefaultNamespace.GenerateCifrasControl" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;mulResponse" type="java:DefaultNamespace.MulResponse" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;resta" type="java:DefaultNamespace.Resta" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;createPdf" type="java:DefaultNamespace.CreatePdf" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;sumResponse" type="java:DefaultNamespace.SumResponse" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;sum" type="java:DefaultNamespace.Sum" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;createPdfResponse" type="java:DefaultNamespace.CreatePdfResponse" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;generarConciliacion" type="java:DefaultNamespace.GenerarConciliacion" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;generarConciliacionResponse" type="java:DefaultNamespace.GenerarConciliacionResponse" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;mul" type="java:DefaultNamespace.Mul" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;generarPdf" type="java:DefaultNamespace.GenerarPdf" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;generarPdfResponse" type="java:DefaultNamespace.GenerarPdfResponse" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;restaResponse" type="java:DefaultNamespace.RestaResponse" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
  </service>
</deployment>