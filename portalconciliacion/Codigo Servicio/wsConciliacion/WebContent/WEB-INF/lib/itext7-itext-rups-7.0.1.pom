<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.itextpdf</groupId>
    <artifactId>itext-rups</artifactId>
    <name>itext-rups</name>
    <version>7.0.1</version>
    <description>Rups, a tool to view PDF structure in a swing gui.</description>
    <url>http://www.itextpdf.com/</url>
    <properties>
        <logback.version>1.1.3</logback.version>
        <dom4j.version>1.6.1</dom4j.version>
        <junit.version>4.8.2</junit.version>
        <bouncycastle.version>1.49</bouncycastle.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <launch4j.version>1.7.11</launch4j.version>
    </properties>
    <mailingLists>
        <mailingList>
            <name>iText Questions</name>
            <subscribe>
                http://lists.sourceforge.net/lists/listinfo/itext-questions
            </subscribe>
            <post><EMAIL></post>
            <archive>
                http://news.gmane.org/gmane.comp.java.lib.itext.general
            </archive>
            <otherArchives>
                <otherArchive>http://itext-general.2136553.n4.nabble.com/</otherArchive>
                <otherArchive>http://www.junlu.com/2.html</otherArchive>
                <otherArchive>http://sourceforge.net/mailarchive/forum.php?forum_id=3273</otherArchive>
                <otherArchive>http://www.mail-archive.com/itext-questions%40lists.sourceforge.net/</otherArchive>
            </otherArchives>
        </mailingList>
    </mailingLists>
    <developers>
        <developer>
            <id>blowagie</id>
            <name>Bruno Lowagie</name>
            <email><EMAIL></email>
            <url>http://www.lowagie.com</url>
        </developer>
        <developer>
            <id>psoares33</id>
            <name>Paulo Soares</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>mstorer</id>
            <name>Mark Storer</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>trumpetinc</id>
            <name>Kevin Day</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>xlv</id>
            <name>Xavier Le Vourch</name>
            <email><EMAIL></email>
            <url>http://www.xlv-labs.com</url>
        </developer>
    </developers>
    <licenses>
        <license>
            <name>GNU Lesser General Public License v2.1</name>
            <url>http://www.gnu.org/licenses/lgpl-2.1.html</url>
        </license>
    </licenses>
    <scm>
        <connection>scm:svn:https://itextrups.svn.sourceforge.net/svnroot/itextrups/trunk/</connection>
        <url>http://itextrups.svn.sourceforge.net/viewvc/itextrups/</url>
    </scm>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.8</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <failOnError>false</failOnError>
                    <links>
                        <link>http://api.itextpdf.com/itext/</link>
                    </links>
                    <footer><![CDATA[
<script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-11854164-1', 'itextpdf.com');
  ga('send', 'pageview');

</script>
    ]]></footer>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.3.1</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <mainClass>com.itextpdf.rups.RupsLauncher</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <archive>
                        <manifest>
                            <mainClass>com.itextpdf.rups.RupsLauncher</mainClass>
                        </manifest>
                    </archive>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id> <!-- this is used for inheritance merges -->
                        <phase>package</phase> <!-- append to the packaging phase. -->
                        <goals>
                            <goal>single</goal> <!-- goals == mojos -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.7</source>
                    <target>1.7</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>external.atlassian.jgitflow</groupId>
                <artifactId>jgitflow-maven-plugin</artifactId>
                <version>1.0-m5.1</version>
                <configuration>
                    <!-- see goals wiki page for configuration options -->
                    <flowInitContext>
                        <masterBranchName>master</masterBranchName>
                        <developBranchName>develop</developBranchName>
                        <featureBranchPrefix>feature/</featureBranchPrefix>
                        <releaseBranchPrefix>release/</releaseBranchPrefix>
                        <hotfixBranchPrefix>hotfix/</hotfixBranchPrefix>
                        <versionTagPrefix />
                    </flowInitContext>
                    <allowUntracked>true</allowUntracked>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                    <updateDependencies>true</updateDependencies>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>exe</id>
            <build>
                <plugins>
                    <plugin>
                        <!-- see http://alakai.org/reference/plugins/launch4j-plugin-usage.html -->
                        <!-- see http://launch4j.sourceforge.net/docs.html for more config options -->
                        <groupId>com.akathist.maven.plugins.launch4j</groupId>
                        <artifactId>launch4j-maven-plugin</artifactId>
                        <version>${launch4j.version}</version>
                        <executions>
                            <execution>
                                <id>l4j-gui</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>launch4j</goal>
                                </goals>
                                <configuration>
                                    <dontWrapJar>false</dontWrapJar>
                                    <headerType>gui</headerType>
                                    <outfile>target/Rups.exe</outfile>
                                    <jar>target/itext-rups-${project.version}-jar-with-dependencies.jar</jar>
                                    <errTitle>Rups</errTitle>
                                    <icon>config/logo.ico</icon>
                                    <classPath>
                                        <mainClass>com.itextpdf.rups.RupsLauncher</mainClass>
                                        <addDependencies>true</addDependencies>
                                        <preCp>anything</preCp>
                                    </classPath>
                                    <jre>
                                        <minVersion>1.7.0</minVersion>
                                    </jre>
                                    <versionInfo>
                                        <fileVersion>*******</fileVersion>
                                        <txtFileVersion>${project.version}</txtFileVersion>
                                        <fileDescription>Rups, object view of pdf content</fileDescription>
                                        <copyright>AGPL</copyright>
                                        <productVersion>*******</productVersion>
                                        <txtProductVersion>${project.version}</txtProductVersion>
                                        <productName>Rups</productName>
                                        <internalName>itext-rups</internalName>
                                        <originalFilename>Rups.exe</originalFilename>
                                    </versionInfo>
                                    <!-- <splash> <file>${basedir}/src/main/installer/images/splash.bmp</file>
                                        <waitForWindow>true</waitForWindow> <timeout>60</timeout> <timeoutErr>false</timeoutErr>
                                        </splash> -->
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <type>jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>${dom4j.version}</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>${bouncycastle.version}</version>
            <type>jar</type>
            <scope>compile</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>${bouncycastle.version}</version>
            <type>jar</type>
            <scope>compile</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>io</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>kernel</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>
    </dependencies>
</project>
