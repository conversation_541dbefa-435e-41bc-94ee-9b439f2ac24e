<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.itextpdf</groupId>
        <artifactId>root</artifactId>
        <version>7.0.1</version>
    </parent>

    <artifactId>hyph</artifactId>
    <name>iText 7 - hyph</name>
    <description>XML files that can be used for hyphenation</description>
    <url>http://itextpdf.com/</url>
    <licenses>
        <license>
            <name>Various licenses (see individual files)</name>
        </license>
    </licenses>

    <build>
        <plugins>
            <plugin>
                <groupId>org.pitest</groupId>
                <artifactId>pitest-maven</artifactId>
                <version>${pitest.version}</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
