<?xml version="1.0" encoding="UTF-8"?><!-- Use this file to deploy some handlers/chains and services      --><!-- Two ways to do this:                                           --><!--   java org.apache.axis.client.AdminClient deploy.wsdd          --><!--      after the axis server is running                          --><!-- or                                                             --><!--   java org.apache.axis.utils.Admin client|server deploy.wsdd   --><!--      from the same directory that the Axis engine runs         --><deployment xmlns="http://xml.apache.org/axis/wsdd/" xmlns:java="http://xml.apache.org/axis/wsdd/providers/java">

  <!-- Services from WsConciliacionService WSDL service -->

  <service name="WsConciliacion" provider="java:RPC" style="wrapped" use="literal">
      <parameter name="wsdlTargetNamespace" value="http://servicioweb.wsconciliacion.coppel.com"/>
      <parameter name="wsdlServiceElement" value="WsConciliacionService"/>
      <parameter name="schemaQualified" value="http://servicioweb.wsconciliacion.coppel.com"/>
      <parameter name="wsdlServicePort" value="WsConciliacion"/>
      <parameter name="className" value="com.coppel.wsconciliacion.servicioweb.WsConciliacion"/>
      <parameter name="wsdlPortType" value="WsConciliacion"/>
      <parameter name="typeMappingVersion" value="1.2"/>
      <operation xmlns:operNS="http://servicioweb.wsconciliacion.coppel.com" xmlns:retNS="http://servicioweb.wsconciliacion.coppel.com" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="reenviarArchivoConnect" qname="operNS:reenviarArchivoConnect" returnQName="retNS:reenviarArchivoConnectReturn" returnType="rtns:string" soapAction="">
        <parameter xmlns:pns="http://servicioweb.wsconciliacion.coppel.com" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:peticion" type="tns:int"/>
      </operation>
      <operation xmlns:operNS="http://servicioweb.wsconciliacion.coppel.com" xmlns:retNS="http://servicioweb.wsconciliacion.coppel.com" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="generarConciliacion" qname="operNS:generarConciliacion" returnQName="retNS:generarConciliacionReturn" returnType="rtns:string" soapAction="">
      </operation>
      <operation xmlns:operNS="http://servicioweb.wsconciliacion.coppel.com" xmlns:retNS="http://servicioweb.wsconciliacion.coppel.com" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="obtenerUltimaFecha" qname="operNS:obtenerUltimaFecha" returnQName="retNS:obtenerUltimaFechaReturn" returnType="rtns:string" soapAction="">
      </operation>
      <operation xmlns:operNS="http://servicioweb.wsconciliacion.coppel.com" xmlns:retNS="http://servicioweb.wsconciliacion.coppel.com" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="reenviarCorreo" qname="operNS:reenviarCorreo" returnQName="retNS:reenviarCorreoReturn" returnType="rtns:string" soapAction="">
        <parameter xmlns:pns="http://servicioweb.wsconciliacion.coppel.com" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:peticion" type="tns:int"/>
      </operation>
      <operation xmlns:operNS="http://servicioweb.wsconciliacion.coppel.com" xmlns:retNS="http://servicioweb.wsconciliacion.coppel.com" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="consultarEstadoConciliacion" qname="operNS:consultarEstadoConciliacion" returnQName="retNS:consultarEstadoConciliacionReturn" returnType="rtns:string" soapAction="">
      </operation>
      <parameter name="allowedMethods" value="consultarEstadoConciliacion obtenerUltimaFecha reenviarArchivoConnect reenviarCorreo generarConciliacion"/>

  </service>
</deployment>