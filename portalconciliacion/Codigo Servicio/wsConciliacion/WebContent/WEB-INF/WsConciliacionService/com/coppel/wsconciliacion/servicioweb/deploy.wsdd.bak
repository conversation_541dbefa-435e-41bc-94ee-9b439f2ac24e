<!-- Use this file to deploy some handlers/chains and services      -->
<!-- Two ways to do this:                                           -->
<!--   java org.apache.axis.client.AdminClient deploy.wsdd          -->
<!--      after the axis server is running                          -->
<!-- or                                                             -->
<!--   java org.apache.axis.utils.Admin client|server deploy.wsdd   -->
<!--      from the same directory that the Axis engine runs         -->

<deployment
    xmlns="http://xml.apache.org/axis/wsdd/"
    xmlns:java="http://xml.apache.org/axis/wsdd/providers/java">

  <!-- Services from WsConciliacionService WSDL service -->

  <service name="WsConciliacion" provider="java:RPC" style="wrapped" use="literal">
      <parameter name="wsdlTargetNamespace" value="http://servicioweb.wsconciliacion.coppel.com"/>
      <parameter name="wsdlServiceElement" value="WsConciliacionService"/>
      <parameter name="schemaQualified" value="http://servicioweb.wsconciliacion.coppel.com"/>
      <parameter name="wsdlServicePort" value="WsConciliacion"/>
      <parameter name="className" value="com.coppel.wsconciliacion.servicioweb.WsConciliacionSoapBindingImpl"/>
      <parameter name="wsdlPortType" value="WsConciliacion"/>
      <parameter name="typeMappingVersion" value="1.2"/>
      <operation name="reenviarArchivoConnect" qname="operNS:reenviarArchivoConnect" xmlns:operNS="http://servicioweb.wsconciliacion.coppel.com" returnQName="retNS:reenviarArchivoConnectReturn" xmlns:retNS="http://servicioweb.wsconciliacion.coppel.com" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <parameter qname="pns:peticion" xmlns:pns="http://servicioweb.wsconciliacion.coppel.com" type="tns:int" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <operation name="generarConciliacion" qname="operNS:generarConciliacion" xmlns:operNS="http://servicioweb.wsconciliacion.coppel.com" returnQName="retNS:generarConciliacionReturn" xmlns:retNS="http://servicioweb.wsconciliacion.coppel.com" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
      </operation>
      <operation name="obtenerUltimaFecha" qname="operNS:obtenerUltimaFecha" xmlns:operNS="http://servicioweb.wsconciliacion.coppel.com" returnQName="retNS:obtenerUltimaFechaReturn" xmlns:retNS="http://servicioweb.wsconciliacion.coppel.com" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
      </operation>
      <operation name="reenviarCorreo" qname="operNS:reenviarCorreo" xmlns:operNS="http://servicioweb.wsconciliacion.coppel.com" returnQName="retNS:reenviarCorreoReturn" xmlns:retNS="http://servicioweb.wsconciliacion.coppel.com" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <parameter qname="pns:peticion" xmlns:pns="http://servicioweb.wsconciliacion.coppel.com" type="tns:int" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <operation name="consultarEstadoConciliacion" qname="operNS:consultarEstadoConciliacion" xmlns:operNS="http://servicioweb.wsconciliacion.coppel.com" returnQName="retNS:consultarEstadoConciliacionReturn" xmlns:retNS="http://servicioweb.wsconciliacion.coppel.com" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
      </operation>
      <parameter name="allowedMethods" value="consultarEstadoConciliacion obtenerUltimaFecha reenviarArchivoConnect reenviarCorreo generarConciliacion"/>

  </service>
</deployment>
