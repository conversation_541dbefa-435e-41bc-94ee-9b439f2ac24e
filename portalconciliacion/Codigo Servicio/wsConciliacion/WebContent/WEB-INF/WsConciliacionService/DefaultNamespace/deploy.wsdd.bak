<!-- Use this file to deploy some handlers/chains and services      -->
<!-- Two ways to do this:                                           -->
<!--   java org.apache.axis.client.AdminClient deploy.wsdd          -->
<!--      after the axis server is running                          -->
<!-- or                                                             -->
<!--   java org.apache.axis.utils.Admin client|server deploy.wsdd   -->
<!--      from the same directory that the Axis engine runs         -->

<deployment
    xmlns="http://xml.apache.org/axis/wsdd/"
    xmlns:java="http://xml.apache.org/axis/wsdd/providers/java">

  <!-- Services from WsConciliacionService WSDL service -->

  <service name="WsConciliacion" provider="java:RPC" style="document" use="literal">
      <parameter name="wsdlTargetNamespace" value="http://DefaultNamespace"/>
      <parameter name="wsdlServiceElement" value="WsConciliacionService"/>
      <parameter name="schemaQualified" value="http://DefaultNamespace"/>
      <parameter name="wsdlServicePort" value="WsConciliacion"/>
      <parameter name="className" value="DefaultNamespace.WsConciliacionSoapBindingImpl"/>
      <parameter name="wsdlPortType" value="WsConciliacion"/>
      <parameter name="typeMappingVersion" value="1.2"/>
      <operation name="obtenerUltimaFecha" qname="operNS:obtenerUltimaFecha" xmlns:operNS="http://DefaultNamespace" returnQName="retNS:obtenerUltimaFechaReturn" xmlns:retNS="http://DefaultNamespace" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
      </operation>
      <operation name="generarConciliacion" qname="operNS:generarConciliacion" xmlns:operNS="http://DefaultNamespace" returnQName="retNS:generarConciliacionReturn" xmlns:retNS="http://DefaultNamespace" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <fault name="IOException" qname="fns:fault" xmlns:fns="http://DefaultNamespace" class="DefaultNamespace.IOException" type="tns:anyType" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <operation name="reenviarCorreo" qname="operNS:reenviarCorreo" xmlns:operNS="http://DefaultNamespace" returnQName="retNS:reenviarCorreoReturn" xmlns:retNS="http://DefaultNamespace" returnType="rtns:string" xmlns:rtns="http://www.w3.org/2001/XMLSchema" soapAction="" >
        <parameter qname="pns:peticion" xmlns:pns="http://DefaultNamespace" type="tns:int" xmlns:tns="http://www.w3.org/2001/XMLSchema"/>
      </operation>
      <parameter name="allowedMethods" value="generarConciliacion reenviarCorreo obtenerUltimaFecha"/>

      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>reenviarCorreoResponse"
        type="java:DefaultNamespace.ReenviarCorreoResponse"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>obtenerUltimaFechaResponse"
        type="java:DefaultNamespace.ObtenerUltimaFechaResponse"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>obtenerUltimaFecha"
        type="java:DefaultNamespace.ObtenerUltimaFecha"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>generarConciliacion"
        type="java:DefaultNamespace.GenerarConciliacion"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>generarConciliacionResponse"
        type="java:DefaultNamespace.GenerarConciliacionResponse"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
      <typeMapping
        xmlns:ns="http://DefaultNamespace"
        qname="ns:>reenviarCorreo"
        type="java:DefaultNamespace.ReenviarCorreo"
        serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
        deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
        encodingStyle=""
      />
  </service>
</deployment>
