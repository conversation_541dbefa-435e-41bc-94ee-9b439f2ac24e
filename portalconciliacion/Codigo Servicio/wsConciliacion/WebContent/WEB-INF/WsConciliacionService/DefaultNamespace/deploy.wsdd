<?xml version="1.0" encoding="UTF-8"?><!-- Use this file to deploy some handlers/chains and services      --><!-- Two ways to do this:                                           --><!--   java org.apache.axis.client.AdminClient deploy.wsdd          --><!--      after the axis server is running                          --><!-- or                                                             --><!--   java org.apache.axis.utils.Admin client|server deploy.wsdd   --><!--      from the same directory that the Axis engine runs         --><deployment xmlns="http://xml.apache.org/axis/wsdd/" xmlns:java="http://xml.apache.org/axis/wsdd/providers/java">

  <!-- Services from WsConciliacionService WSDL service -->

  <service name="WsConciliacion" provider="java:RPC" style="document" use="literal">
      <parameter name="wsdlTargetNamespace" value="http://DefaultNamespace"/>
      <parameter name="wsdlServiceElement" value="WsConciliacionService"/>
      <parameter name="schemaQualified" value="http://DefaultNamespace"/>
      <parameter name="wsdlServicePort" value="WsConciliacion"/>
      <parameter name="className" value="WsConciliacion"/>
      <parameter name="wsdlPortType" value="WsConciliacion"/>
      <parameter name="typeMappingVersion" value="1.2"/>
      <operation xmlns:operNS="http://DefaultNamespace" xmlns:retNS="http://DefaultNamespace" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="obtenerUltimaFecha" qname="operNS:obtenerUltimaFecha" returnQName="retNS:obtenerUltimaFechaReturn" returnType="rtns:string" soapAction="">
      </operation>
      <operation xmlns:operNS="http://DefaultNamespace" xmlns:retNS="http://DefaultNamespace" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="generarConciliacion" qname="operNS:generarConciliacion" returnQName="retNS:generarConciliacionReturn" returnType="rtns:string" soapAction="">
        <fault xmlns:fns="http://DefaultNamespace" xmlns:tns="http://www.w3.org/2001/XMLSchema" name="IOException" qname="fns:fault" class="DefaultNamespace.IOException" type="tns:anyType"/>
      </operation>
      <operation xmlns:operNS="http://DefaultNamespace" xmlns:retNS="http://DefaultNamespace" xmlns:rtns="http://www.w3.org/2001/XMLSchema" name="reenviarCorreo" qname="operNS:reenviarCorreo" returnQName="retNS:reenviarCorreoReturn" returnType="rtns:string" soapAction="">
        <parameter xmlns:pns="http://DefaultNamespace" xmlns:tns="http://www.w3.org/2001/XMLSchema" qname="pns:peticion" type="tns:int"/>
      </operation>
      <parameter name="allowedMethods" value="generarConciliacion reenviarCorreo obtenerUltimaFecha"/>

      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;reenviarCorreoResponse" type="java:DefaultNamespace.ReenviarCorreoResponse" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;obtenerUltimaFechaResponse" type="java:DefaultNamespace.ObtenerUltimaFechaResponse" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;obtenerUltimaFecha" type="java:DefaultNamespace.ObtenerUltimaFecha" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;generarConciliacion" type="java:DefaultNamespace.GenerarConciliacion" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;generarConciliacionResponse" type="java:DefaultNamespace.GenerarConciliacionResponse" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
      <typeMapping xmlns:ns="http://DefaultNamespace" qname="ns:&gt;reenviarCorreo" type="java:DefaultNamespace.ReenviarCorreo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle=""/>
  </service>
</deployment>