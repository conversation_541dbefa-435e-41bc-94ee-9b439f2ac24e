<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="src" path="resources"/>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.module.container"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/axis.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-discovery-0.2.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-logging.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/gson-2.2.4.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-barcodes-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-font-asian-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-forms-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-hyph-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-io-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-kernel-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-layout-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-pdfa-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-sign-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/bcprov-jdk18on-1.79.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/bcpkix-jdk18on-1.79.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jaxrpc.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/mail.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/postgresql-42.7.5.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/saaj.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/wsdl4j.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/activation.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/javax.jws-api-1.1.jar"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8">
		<attributes>
			<attribute name="module" value="true"/>
			<attribute name="owner.project.facets" value="java"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jsch-0.1.54.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jcifs-1.3.18.jar"/>
	<classpathentry kind="output" path="build/classes"/>
</classpath>
