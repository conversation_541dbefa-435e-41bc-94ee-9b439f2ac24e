<html>
<head>
	<link rel="stylesheet" href="../../../utilidadesweb/plantilla/coppel/assets/css/font-awesome.min.css" />
	<link rel="stylesheet" href="../../../utilidadesweb/plantilla/coppel/assets/css/jquery-ui-1.10.3.full.min.css" />
	<link rel="stylesheet" href="../../../utilidadesweb/plantilla/coppel/assets/css/datepicker.css" />
	<link rel="stylesheet" href="../../../utilidadesweb/plantilla/coppel/assets/css/ui.jqgrid.css" />
	<link rel="stylesheet" href="../../../utilidadesweb/plantilla/coppel/assets/css/ace.min.css" />
	<link rel="stylesheet" href="../../../utilidadesweb/plantilla/coppel/assets/css/ace-responsive.min.css" />
	<link rel="stylesheet" href="../../../utilidadesweb/plantilla/coppel/assets/css/ace-skins.min.css" />

	<title></title>
	<link rel="stylesheet" type="text/css" href="files/css/frmGenerarConciliacion.css">
	<style media="screen">
		.mainform{
			margin:0 auto;
			width: 500px;
			padding:6px;
		}
	</style>
</head>
<body>

	<div class="mainform">
		<div class="widget-box">
			<div class="widget-header widget-header-small">
				<!-- HEADER  -->
				<label class="titulo" >Generar archivo de conciliaci&oacute;n</label>
				<label class="fecha-conciliacion" id="fecha-conciliacion"/>
			</div>
			<div class="widget-body">
				<div class="widget-main">
					<!-- BODY -->
					<div class="separador">
						<div id="contenedor">
							<div class="loader" id="loader">Loading...</div>
						</div>
					</div>
					<div class="footer">
						<input id="btnConciliar" class="btn btn-small btn-primary" style="width:120px;" value="Generar archivo" type="button" onclick="generarConciliacion($(this))"/>
						<br>
						<label class="estado">Estado: En espera</label>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div id="fade" class="overlay"></div>

	  <div id="contenedorModalCorrecto" class="modal">
	  	<h2 style="background:#00FF5B; text-align:center; height:30px;">Correcto</h2>
	     <label id="mensaje-ws" class="mensaje-escrito"></label>
	     <div class="contenedor-boton">
	     	<input id="Aceptar" style="width:120px;" value="Aceptar" class="btn btn-small btn-primary" type="button" name="aceptar" onclick="ocultarModal()" />
	     </div>
	  </div>

	  <div id="contenedorModalAlerta" class="modal">
	  	<h2 style="background:#E8C335; text-align:center; height:30px;">Alerta</h2>
	     <label id="mensaje-ws" class="mensaje-escrito"></label>
	     <div class="contenedor-boton">
	     	<input id="Aceptar" style="width:120px;" value="Aceptar" class="btn btn-small btn-primary" type="button" name="aceptar" onclick="ocultarModal()" />
	     </div>
	  </div>

	  <div id="contenedorModalError" class="modal">
	  	 <h2 style="background:#FF3648; text-align:center; height:30px;">Error</h2>
		 <label id="mensaje-ws" class="mensaje-escrito"></label>
	     <div class="contenedor-boton">
	     	<input id="Aceptar" style="width:120px;" value="Aceptar" class="btn btn-small btn-primary" type="button" name="aceptar" onclick="ocultarModal()" />
	     </div>
	  </div>

	  <div id="contenedorModalErrorMensaje" class="modal" style="height: 200px;">
	  	 <h2 style="background:#E8C335; text-align:center; height:30px;">Alerta</h2>
		 <label id="mensaje-ws" class="mensaje-escrito"></label>
		 <label>¿Desea reenviar el correo?, los archivos seran eliminados despues de la acción que seleccione.</label>
	     <div class="contenedor-boton">
	     	<input id="Aceptar" style="width:120px; left: 70px;" value="Si" class="button boton-modal boton-modal-aceptar" type="button" name="aceptar" onclick="reenvioDeCorreo('reenvioDeCorreo',1)" />
	     	<input id="Aceptar" style="width:120px; left: 210px;" value="No" class="button boton-modal boton-modal-cancelar" type="button" name="cancelar" onclick="reenvioDeCorreo('reenvioDeCorreo',0)" />
	     </div>
	  </div>
</body>

<script src="files/js/jquery-3.1.1.min.js"></script>
<script src="files/js/frmGenerarConciliacion.js" type="text/javascript" charset="utf-8" async defer></script>
</html>
