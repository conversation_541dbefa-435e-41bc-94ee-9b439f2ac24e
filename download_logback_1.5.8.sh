#!/bin/bash

# Script para descargar Logback 1.5.8 si es necesario
# Solo ejecutar si realmente se requiere Logback en el proyecto

LIB_DIR="portalconciliacion/Codigo Servicio/wsConciliacion/WebContent/WEB-INF/lib"
TEMP_DIR="temp_logback"

# Crear directorio temporal
mkdir -p "$TEMP_DIR"

echo "Descargando Logback 1.5.8..."

# Logback 1.5.8 JAR files
LOGBACK_VERSION="1.5.8"
LOGBACK_BASE_URL="https://repo1.maven.org/maven2/ch/qos/logback"

# Core Logback modules
curl -L "${LOGBACK_BASE_URL}/logback-core/${LOGBACK_VERSION}/logback-core-${LOGBACK_VERSION}.jar" -o "${TEMP_DIR}/logback-core-${LOGBACK_VERSION}.jar"
curl -L "${LOGBACK_BASE_URL}/logback-classic/${LOGBACK_VERSION}/logback-classic-${LOGBACK_VERSION}.jar" -o "${TEMP_DIR}/logback-classic-${LOGBACK_VERSION}.jar"

# SLF4J API (requerido por Logback)
SLF4J_VERSION="2.0.16"
SLF4J_BASE_URL="https://repo1.maven.org/maven2/org/slf4j"
curl -L "${SLF4J_BASE_URL}/slf4j-api/${SLF4J_VERSION}/slf4j-api-${SLF4J_VERSION}.jar" -o "${TEMP_DIR}/slf4j-api-${SLF4J_VERSION}.jar"

echo "Descarga completada. Archivos en ${TEMP_DIR}/"
ls -la "${TEMP_DIR}/"

echo ""
echo "NOTA: Solo copia estos archivos si realmente necesitas Logback en tu proyecto."
echo "Para copiar los archivos ejecuta:"
echo "cp ${TEMP_DIR}/*.jar ${LIB_DIR}/"
echo ""
echo "También necesitarás actualizar el archivo .classpath para incluir estos JARs."
