#!/bin/bash

# Script to download iText 7.1.19 and BouncyCastle 1.79 JAR files
# This script downloads the required JAR files for the migration

LIB_DIR="portalconciliacion/Codigo Servicio/wsConciliacion/WebContent/WEB-INF/lib"
TEMP_DIR="temp_downloads"

# Create temporary directory
mkdir -p "$TEMP_DIR"

echo "Downloading iText 7.1.19 JAR files..."

# iText 7.1.19 JAR files
ITEXT_VERSION="7.1.19"
ITEXT_BASE_URL="https://repo1.maven.org/maven2/com/itextpdf"

# Core iText modules
curl -L "${ITEXT_BASE_URL}/kernel/${ITEXT_VERSION}/kernel-${ITEXT_VERSION}.jar" -o "${TEMP_DIR}/itext7-kernel-${ITEXT_VERSION}.jar"
curl -L "${ITEXT_BASE_URL}/io/${ITEXT_VERSION}/io-${ITEXT_VERSION}.jar" -o "${TEMP_DIR}/itext7-io-${ITEXT_VERSION}.jar"
curl -L "${ITEXT_BASE_URL}/layout/${ITEXT_VERSION}/layout-${ITEXT_VERSION}.jar" -o "${TEMP_DIR}/itext7-layout-${ITEXT_VERSION}.jar"
curl -L "${ITEXT_BASE_URL}/forms/${ITEXT_VERSION}/forms-${ITEXT_VERSION}.jar" -o "${TEMP_DIR}/itext7-forms-${ITEXT_VERSION}.jar"
curl -L "${ITEXT_BASE_URL}/pdfa/${ITEXT_VERSION}/pdfa-${ITEXT_VERSION}.jar" -o "${TEMP_DIR}/itext7-pdfa-${ITEXT_VERSION}.jar"
curl -L "${ITEXT_BASE_URL}/sign/${ITEXT_VERSION}/sign-${ITEXT_VERSION}.jar" -o "${TEMP_DIR}/itext7-sign-${ITEXT_VERSION}.jar"
curl -L "${ITEXT_BASE_URL}/barcodes/${ITEXT_VERSION}/barcodes-${ITEXT_VERSION}.jar" -o "${TEMP_DIR}/itext7-barcodes-${ITEXT_VERSION}.jar"
curl -L "${ITEXT_BASE_URL}/hyph/${ITEXT_VERSION}/hyph-${ITEXT_VERSION}.jar" -o "${TEMP_DIR}/itext7-hyph-${ITEXT_VERSION}.jar"
curl -L "${ITEXT_BASE_URL}/font-asian/${ITEXT_VERSION}/font-asian-${ITEXT_VERSION}.jar" -o "${TEMP_DIR}/itext7-font-asian-${ITEXT_VERSION}.jar"

echo "Downloading BouncyCastle 1.79 JAR files..."

# BouncyCastle 1.79 JAR files
BC_VERSION="1.79"
BC_BASE_URL="https://repo1.maven.org/maven2/org/bouncycastle"

curl -L "${BC_BASE_URL}/bcprov-jdk18on/${BC_VERSION}/bcprov-jdk18on-${BC_VERSION}.jar" -o "${TEMP_DIR}/bcprov-jdk18on-${BC_VERSION}.jar"
curl -L "${BC_BASE_URL}/bcpkix-jdk18on/${BC_VERSION}/bcpkix-jdk18on-${BC_VERSION}.jar" -o "${TEMP_DIR}/bcpkix-jdk18on-${BC_VERSION}.jar"

echo "Download completed. Files are in ${TEMP_DIR}/"
ls -la "${TEMP_DIR}/"

echo "To complete the migration:"
echo "1. Remove old iText 7.0.1 JAR files from ${LIB_DIR}"
echo "2. Copy new JAR files from ${TEMP_DIR} to ${LIB_DIR}"
echo "3. Update .classpath file"
